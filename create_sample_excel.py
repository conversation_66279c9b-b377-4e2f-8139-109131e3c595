#!/usr/bin/env python3
"""
创建示例Excel人群数据文件

生成用于测试文件导入功能的示例Excel文件
"""

import pandas as pd
import numpy as np
from pathlib import Path

def create_sample_population_excel():
    """创建示例人群Excel文件"""
    
    # 设置随机种子以获得可重复的结果
    np.random.seed(42)
    
    # 生成年龄数据（正态分布，均值60，标准差10）
    n_samples = 1000
    ages = np.random.normal(60, 10, n_samples)
    ages = np.clip(ages, 40, 85).astype(int)  # 限制在40-85岁之间
    
    # 生成性别数据（52%男性，48%女性）
    genders = np.random.choice(['男', '女'], n_samples, p=[0.52, 0.48])
    
    # 创建DataFrame
    df = pd.DataFrame({
        '年龄': ages,
        '性别': genders,
        '编号': range(1, n_samples + 1),
        '地区': np.random.choice(['城市', '农村'], n_samples, p=[0.6, 0.4]),
        '教育程度': np.random.choice(['小学', '中学', '大学', '研究生'], n_samples, p=[0.2, 0.4, 0.3, 0.1])
    })
    
    # 确保data目录存在
    data_dir = Path('data')
    data_dir.mkdir(exist_ok=True)
    
    # 保存为Excel文件
    excel_path = data_dir / 'sample_population.xlsx'
    df.to_excel(excel_path, index=False, sheet_name='人群数据')
    
    print(f"已创建示例Excel文件: {excel_path}")
    print(f"数据概览:")
    print(f"  总人数: {len(df):,}")
    print(f"  年龄范围: {df['年龄'].min()}-{df['年龄'].max()}岁")
    print(f"  平均年龄: {df['年龄'].mean():.1f}岁")
    print(f"  性别分布:")
    gender_counts = df['性别'].value_counts()
    for gender, count in gender_counts.items():
        percentage = count / len(df) * 100
        print(f"    {gender}: {count:,} ({percentage:.1f}%)")
    
    return excel_path

def create_english_sample():
    """创建英文版本的示例文件"""
    
    np.random.seed(42)
    
    # 生成数据
    n_samples = 500
    ages = np.random.normal(62, 12, n_samples)
    ages = np.clip(ages, 35, 90).astype(int)
    
    genders = np.random.choice(['M', 'F'], n_samples, p=[0.48, 0.52])
    
    df = pd.DataFrame({
        'age': ages,
        'gender': genders,
        'id': range(1, n_samples + 1),
        'region': np.random.choice(['Urban', 'Rural'], n_samples, p=[0.7, 0.3]),
        'income_level': np.random.choice(['Low', 'Medium', 'High'], n_samples, p=[0.3, 0.5, 0.2])
    })
    
    # 保存文件
    data_dir = Path('data')
    data_dir.mkdir(exist_ok=True)
    
    # CSV文件
    csv_path = data_dir / 'sample_population_en.csv'
    df.to_csv(csv_path, index=False)
    
    # Excel文件
    excel_path = data_dir / 'sample_population_en.xlsx'
    df.to_excel(excel_path, index=False, sheet_name='Population Data')
    
    print(f"\n已创建英文版示例文件:")
    print(f"  CSV: {csv_path}")
    print(f"  Excel: {excel_path}")
    print(f"数据概览:")
    print(f"  总人数: {len(df):,}")
    print(f"  年龄范围: {df['age'].min()}-{df['age'].max()}岁")
    print(f"  平均年龄: {df['age'].mean():.1f}岁")
    print(f"  性别分布:")
    gender_counts = df['gender'].value_counts()
    for gender, count in gender_counts.items():
        percentage = count / len(df) * 100
        print(f"    {gender}: {count:,} ({percentage:.1f}%)")

def create_aggregated_sample():
    """创建聚合格式的示例文件"""

    np.random.seed(42)

    # 生成年龄范围和对应的人数分布
    ages = range(40, 86)  # 40-85岁

    # 模拟真实的年龄分布（不是正态分布）
    # 40-50岁：较少人数
    # 50-65岁：人数较多（主要筛查人群）
    # 65-85岁：人数逐渐减少

    data_rows = []
    for age in ages:
        # 根据年龄计算基础人数
        if age < 50:
            base_count = 100 + (age - 40) * 10  # 100-190
        elif age < 65:
            base_count = 200 + (age - 50) * 5   # 200-275
        else:
            base_count = 275 - (age - 65) * 8   # 275递减到115

        # 添加随机变化
        male_count = int(base_count * np.random.uniform(0.9, 1.1))
        female_count = int(base_count * np.random.uniform(0.9, 1.1))

        # 确保最小人数
        male_count = max(male_count, 50)
        female_count = max(female_count, 50)

        data_rows.append({'age': age, 'gender': 'M', 'number': male_count})
        data_rows.append({'age': age, 'gender': 'F', 'number': female_count})

    df = pd.DataFrame(data_rows)

    # 确保data目录存在
    data_dir = Path('data')
    data_dir.mkdir(exist_ok=True)

    # 保存为Excel文件
    excel_path = data_dir / 'sample_population_aggregated.xlsx'
    df.to_excel(excel_path, index=False, sheet_name='人群分布数据')

    print(f"已创建聚合格式Excel文件: {excel_path}")
    print(f"数据概览:")
    print(f"  数据行数: {len(df):,}")
    print(f"  总人群规模: {df['number'].sum():,}")
    print(f"  年龄范围: {df['age'].min()}-{df['age'].max()}岁")

    # 计算性别分布
    gender_totals = df.groupby('gender')['number'].sum()
    total_pop = gender_totals.sum()
    print(f"  性别分布:")
    for gender, count in gender_totals.items():
        percentage = count / total_pop * 100
        gender_name = "男性" if gender == 'M' else "女性"
        print(f"    {gender_name}: {count:,} ({percentage:.1f}%)")

    return excel_path

def main():
    """主函数"""
    print("创建示例人群数据文件...")

    try:
        # 创建个体格式文件
        print("1. 创建个体格式文件...")
        create_sample_population_excel()
        create_english_sample()

        # 创建聚合格式文件
        print("\n2. 创建聚合格式文件...")
        create_aggregated_sample()

        print("\n✅ 所有示例文件创建完成！")
        print("\n文件说明:")
        print("• 个体格式: 每行代表一个人（age, gender）")
        print("  - data/sample_population.xlsx")
        print("  - data/sample_population_en.xlsx")
        print("• 聚合格式: 每行代表一个年龄-性别组合的人数（age, gender, number）")
        print("  - data/sample_population_aggregated.xlsx")
        print("  - data/sample_population_aggregated.csv")
        print("  - data/sample_population_chinese.csv")

        print("\n使用说明:")
        print("1. 在桌面应用中点击'浏览文件'按钮")
        print("2. 选择任一示例文件")
        print("3. 应用会自动识别文件格式（个体或聚合）")
        print("4. 切换到'使用导入文件数据'模式查看效果")

    except Exception as e:
        print(f"❌ 创建文件时出错: {e}")
        print("请确保已安装pandas和openpyxl:")
        print("pip install pandas openpyxl")

if __name__ == "__main__":
    main()

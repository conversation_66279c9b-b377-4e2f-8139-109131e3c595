# Story 1.4: 生命表集成和死亡率建模

## Status
Ready for Review

## Story
**As a** 模拟引擎，
**I want** 基于中国生命表应用自然死亡率，
**so that** 准确建模人群老龄化和自然死亡。

## Acceptance Criteria
1. 实现生命表数据加载和验证系统
2. 创建年龄和性别特异性死亡率计算函数
3. 实现随机抽样的年度死亡率应用
4. 添加人群生存统计跟踪
5. 创建生命表数据格式文档
6. 实现死亡率计算单元测试

## Tasks / Subtasks

- [x] 任务1：实现生命表数据管理系统 (AC: 1)
  - [x] 创建src/modules/population/life_table.py文件
  - [x] 实现LifeTable类，加载和管理生命表数据
  - [x] 添加CSV格式生命表数据解析功能
  - [x] 实现数据验证（年龄范围、死亡率范围检查）
  - [x] 添加多个生命表支持（中国、WHO全球等）
  - [x] 实现生命表数据缓存和索引优化

- [x] 任务2：创建死亡率计算引擎 (AC: 2)
  - [x] 在LifeTable类中实现get_mortality_rate方法
  - [x] 添加年龄和性别特异性死亡率查询
  - [x] 实现死亡率插值计算（处理非整数年龄以及非连贯年龄）
  - [x] 添加死亡率平滑处理功能
  - [x] 实现死亡率趋势调整（年份校正）
  - [x] 添加死亡率不确定性建模支持

- [x] 任务3：实现年度死亡率应用机制 (AC: 3)
  - [x] 创建src/core/mortality_engine.py文件
  - [x] 实现MortalityEngine类，管理死亡率应用
  - [x] 添加随机抽样死亡判定功能
  - [x] 实现批量死亡率计算和应用
  - [x] 添加死亡原因分类（自然死亡vs癌症死亡）
  - [x] 实现死亡时间精确计算（月份级别）

- [x] 任务4：添加人群生存统计跟踪 (AC: 4)
  - [x] 扩展Population类，添加生存统计功能
  - [x] 实现生存曲线计算和跟踪
  - [x] 添加年龄特异性生存率统计
  - [x] 实现队列生存分析功能
  - [x] 添加生存时间分布计算
  - [x] 创建生存统计可视化准备功能

- [x] 任务5：创建生命表数据和文档 (AC: 5)
  - [x] 创建data/life_tables/目录结构
  - [x] 添加中国2020年生命表数据（china_2020.csv）
  - [x] 添加WHO全球生命表数据（who_global.csv）
  - [x] 创建生命表数据格式规范文档
  - [x] 编写生命表使用指南和API文档
  - [x] 添加数据来源和更新说明

- [x] 任务6：实现死亡率建模测试套件 (AC: 6)
  - [x] 创建tests/unit/test_life_table.py测试文件
  - [x] 创建tests/unit/test_mortality_engine.py测试文件
  - [x] 实现生命表数据加载和验证测试
  - [x] 添加死亡率计算准确性测试
  - [x] 创建随机抽样死亡判定测试
  - [x] 实现生存统计计算验证测试

## Dev Notes

### 生命表数据格式
```xlsx
# data/life_tables/china_2020.xlsx
age,gender,mortality_rate
0,male,0.00654
0,female,0.00521
1,male,0.00043
1,female,0.00035
...
50,male,0.00312
50,female,0.00198
...
```

### LifeTable类核心方法
- `load_life_table(file_path)`: 加载生命表数据
- `get_mortality_rate(age, gender)`: 获取特定年龄性别死亡率
- `get_survival_probability(age, gender)`: 计算特定年龄性别生存概率
- `interpolate_rate(age, gender)`: 非整数年龄及非连贯年龄插值
- `validate_data()`: 数据完整性验证
- `get_life_expectancy(age, gender)`: 计算特定年龄性别预期寿命

### MortalityEngine类核心功能
- **死亡判定**: 基于概率的随机死亡判定
- **批量处理**: 高效处理大规模人群死亡率
- **时间精度**: 支持月份级别的死亡时间计算
- **死亡分类**: 区分自然死亡和疾病相关死亡
- **统计跟踪**: 记录死亡率应用统计信息

### 死亡率计算逻辑
```python
def apply_mortality(individual, current_time):
    """应用年度死亡率"""
    age = individual.get_age_at_time(current_time)
    mortality_rate = life_table.get_mortality_rate(age, individual.gender)
    
    # 转换为月度死亡概率
    monthly_rate = 1 - (1 - mortality_rate) ** (1/12)
    
    # 随机判定是否死亡
    if random.random() < monthly_rate:
        individual.transition_to_state(DiseaseState.DEATH_OTHER, current_time)
        return True
    return False
```

### 生存统计功能
- **生存曲线**: Kaplan-Meier生存估计
- **生存率**: 年龄特异性生存率计算
- **中位生存时间**: 队列中位生存时间
- **生存分析**: 按性别、年龄组的生存分析
- **风险评估**: 死亡风险评估和预测

### 数据验证规则
- 死亡率范围: 0 ≤ mortality_rate ≤ 1
- 年龄范围: 0 ≤ age ≤ 100
- 性别值: "male", "female"
- 生存概率: survival_probability = 1 - mortality_rate
- 数据完整性: 所有年龄性别组合都有数据

### 性能优化
- 生命表数据预加载和索引
- 批量死亡率计算减少查询次数
- 使用NumPy向量化操作提高计算效率
- 实现死亡率查询缓存机制

### Testing
#### 测试文件位置
- `tests/unit/test_life_table.py`
- `tests/unit/test_mortality_engine.py`
- `tests/integration/test_mortality_application.py`

#### 测试标准
- 生命表数据加载正确性验证
- 死亡率计算精度测试（与标准值比较）
- 随机抽样统计检验（大数定律验证）
- 生存统计计算准确性测试
- 边界条件和异常处理测试

#### 测试框架和模式
- 使用pytest fixtures提供测试生命表数据
- 统计检验验证随机抽样准确性
- Mock随机数生成器测试确定性行为
- 性能测试验证大规模死亡率计算

#### 特定测试要求
- 死亡率精度: 计算结果与标准值误差 < 0.1%
- 统计准确性: 大样本随机抽样偏差 < 5%
- 性能要求: 10万个体死亡率计算 < 5秒
- 数据完整性: 所有年龄性别组合都能正确查询

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-07-31 | 1.0 | 初始故事创建 | Scrum Master |

## Dev Agent Record
*此部分将由开发代理在实施过程中填写*

### Agent Model Used
Claude Sonnet 4 (Augment Agent)

### Debug Log References
- 修复了生命表模块中未使用的导入项（csv, Tuple, ParameterValidationError）
- 解决了死亡率引擎中时间精度转换的数学计算问题
- 修复了人群生存统计中空数组的统计计算问题
- 优化了Kaplan-Meier生存曲线计算的性能和准确性

### Completion Notes List
- 成功实现LifeTable类，支持CSV和Excel格式的生命表数据加载
- 创建完整的数据验证系统，确保生命表数据质量和完整性
- 实现MortalityEngine类，支持个体和人群级别的死亡率应用
- 扩展Population类，添加详细的生存统计和生存曲线计算功能
- 创建中国2020年和WHO全球生命表数据文件
- 编写完整的API文档和使用指南
- 实现47个单元测试和12个集成测试，覆盖所有核心功能
- 支持多种时间精度（月度、季度、年度）的死亡率应用
- 实现随机种子控制确保结果可重现
- 支持死亡原因分类（自然死亡vs癌症死亡）

### File List
**新建文件：**
- src/modules/population/life_table.py - 生命表数据管理核心实现
- src/core/mortality_engine.py - 死亡率引擎核心实现
- data/life_tables/china_2020.csv - 中国2020年生命表数据
- data/life_tables/china_2020_lifetable.xlsx - 中国2020年生命表数据
- data/life_tables/who_global.csv - WHO全球生命表数据
- docs/api/life-table-data-format.md - 生命表数据格式规范文档
- docs/api/life-table-usage-guide.md - 生命表使用指南
- tests/unit/test_life_table.py - 生命表单元测试（25个测试）
- tests/unit/test_mortality_engine.py - 死亡率引擎单元测试（22个测试）
- tests/integration/test_mortality_application.py - 死亡率应用集成测试（12个测试）

**修改文件：**
- src/modules/population/__init__.py - 添加生命表模块导出
- src/core/__init__.py - 添加死亡率引擎模块导出
- src/core/population.py - 扩展生存统计功能

## QA Results
*此部分将由QA代理在完成故事实施的QA审查后填写*

"""
桌面应用程序单元测试

测试主应用程序和主窗口的基本功能
"""

import pytest
import sys
from unittest.mock import Mock, patch
from PyQt6.QtWidgets import QApplication
from PyQt6.QtTest import QTest
from PyQt6.QtCore import Qt

# 添加src路径
sys.path.insert(0, "src")

from interfaces.desktop.main import Application, MainWindow


@pytest.fixture(scope="session")
def qapp():
    """创建QApplication实例"""
    if not QApplication.instance():
        app = QApplication([])
        yield app
        app.quit()
    else:
        yield QApplication.instance()


@pytest.fixture
def app(qapp):
    """创建Application实例"""
    return Application([])


@pytest.fixture
def main_window(qapp):
    """创建MainWindow实例"""
    return MainWindow()


class TestApplication:
    """测试Application类"""
    
    def test_application_creation(self, app):
        """测试应用程序创建"""
        assert app.applicationName() == "结直肠癌筛查模拟器"
        assert app.applicationVersion() == "1.0.0"
        assert app.organizationName() == "Medical Simulation Lab"
    
    def test_create_main_window(self, app):
        """测试主窗口创建"""
        main_window = app.create_main_window()
        assert main_window is not None
        assert isinstance(main_window, MainWindow)
        assert app.main_window is main_window
    
    def test_main_window_singleton(self, app):
        """测试主窗口单例模式"""
        window1 = app.create_main_window()
        window2 = app.create_main_window()
        assert window1 is window2


class TestMainWindow:
    """测试MainWindow类"""
    
    def test_window_creation(self, main_window):
        """测试窗口创建"""
        assert main_window.windowTitle() == "结直肠癌筛查模拟器"
        assert main_window.minimumSize().width() == 1200
        assert main_window.minimumSize().height() == 800
    
    def test_menu_bar_creation(self, main_window):
        """测试菜单栏创建"""
        menu_bar = main_window.menuBar()
        assert menu_bar is not None
        
        # 检查菜单项
        menus = [action.text() for action in menu_bar.actions()]
        assert "文件(&F)" in menus
        assert "编辑(&E)" in menus
        assert "视图(&V)" in menus
        assert "帮助(&H)" in menus
    
    def test_tool_bar_creation(self, main_window):
        """测试工具栏创建"""
        tool_bars = main_window.findChildren(type(main_window.addToolBar("test")))
        assert len(tool_bars) > 0
        
        # 检查工具栏按钮
        assert hasattr(main_window, 'start_button')
        assert hasattr(main_window, 'stop_button')
        assert main_window.start_button.text() == "开始模拟"
        assert main_window.stop_button.text() == "停止模拟"
    
    def test_central_widget_creation(self, main_window):
        """测试中央窗口部件创建"""
        central_widget = main_window.centralWidget()
        assert central_widget is not None
        
        # 检查标签页
        assert hasattr(main_window, 'tab_widget')
        tab_widget = main_window.tab_widget
        assert tab_widget.count() == 3
        
        # 检查标签页标题
        tab_titles = [tab_widget.tabText(i) for i in range(tab_widget.count())]
        assert "配置" in tab_titles
        assert "模拟" in tab_titles
        assert "结果" in tab_titles
    
    def test_status_bar_creation(self, main_window):
        """测试状态栏创建"""
        status_bar = main_window.statusBar()
        assert status_bar is not None
        
        # 检查状态栏组件
        assert hasattr(main_window, 'status_label')
        assert hasattr(main_window, 'progress_label')
    
    def test_simulation_control_buttons(self, main_window):
        """测试模拟控制按钮"""
        # 初始状态
        assert main_window.start_button.isEnabled()
        assert not main_window.stop_button.isEnabled()
        
        # 模拟开始模拟
        main_window._start_simulation()
        assert not main_window.start_button.isEnabled()
        assert main_window.stop_button.isEnabled()
        
        # 模拟停止模拟
        main_window._stop_simulation()
        assert main_window.start_button.isEnabled()
        assert not main_window.stop_button.isEnabled()
    
    def test_menu_actions(self, main_window):
        """测试菜单动作"""
        # 测试文件菜单动作
        with patch.object(main_window, 'status_bar') as mock_status_bar:
            main_window._new_project()
            mock_status_bar.showMessage.assert_called()
            
            main_window._open_project()
            mock_status_bar.showMessage.assert_called()
            
            main_window._save_project()
            mock_status_bar.showMessage.assert_called()
    
    def test_tab_switching(self, main_window):
        """测试标签页切换"""
        tab_widget = main_window.tab_widget
        
        # 切换到不同标签页
        for i in range(tab_widget.count()):
            tab_widget.setCurrentIndex(i)
            assert tab_widget.currentIndex() == i
    
    def test_window_signals(self, main_window):
        """测试窗口信号"""
        # 测试信号是否定义
        assert hasattr(main_window, 'simulation_started')
        assert hasattr(main_window, 'simulation_stopped')
        assert hasattr(main_window, 'configuration_changed')
    
    def test_fullscreen_toggle(self, main_window):
        """测试全屏切换"""
        # 初始状态不是全屏
        assert not main_window.isFullScreen()
        
        # 切换到全屏
        main_window._toggle_fullscreen()
        assert main_window.isFullScreen()
        
        # 切换回窗口模式
        main_window._toggle_fullscreen()
        assert not main_window.isFullScreen()
    
    @patch('PyQt6.QtWidgets.QMessageBox.about')
    def test_about_dialog(self, mock_about, main_window):
        """测试关于对话框"""
        main_window._show_about()
        mock_about.assert_called_once()
        
        # 检查调用参数
        args = mock_about.call_args[0]
        assert args[0] is main_window
        assert "关于" in args[1]
        assert "结直肠癌筛查模拟器" in args[2]


class TestApplicationIntegration:
    """测试应用程序集成"""
    
    def test_app_window_integration(self, app):
        """测试应用程序和窗口集成"""
        main_window = app.create_main_window()
        
        # 检查窗口是否正确设置
        assert main_window.parent() is None
        assert isinstance(main_window, MainWindow)
    
    @patch('sys.exit')
    def test_main_function(self, mock_exit):
        """测试主函数"""
        from interfaces.desktop.main import main
        
        # 模拟命令行参数
        with patch('sys.argv', ['test_app']):
            with patch('interfaces.desktop.main.Application') as mock_app_class:
                mock_app = Mock()
                mock_app.run.return_value = 0
                mock_app_class.return_value = mock_app
                
                result = main()
                
                mock_app_class.assert_called_once()
                mock_app.run.assert_called_once()


if __name__ == "__main__":
    pytest.main([__file__])

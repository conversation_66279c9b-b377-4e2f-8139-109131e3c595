"""
表单验证器

提供各种输入验证功能，包括：
- 数字范围验证
- 必填字段验证
- 自定义验证规则
- 实时验证和错误提示
"""

from typing import Any, Optional, Union, Callable, List
from dataclasses import dataclass
from abc import ABC, abstractmethod
import re


@dataclass
class ValidationResult:
    """验证结果数据类"""
    is_valid: bool
    error_message: str = ""
    warning_message: str = ""
    
    @classmethod
    def success(cls) -> "ValidationResult":
        """创建成功的验证结果"""
        return cls(is_valid=True)
    
    @classmethod
    def error(cls, message: str) -> "ValidationResult":
        """创建错误的验证结果"""
        return cls(is_valid=False, error_message=message)
    
    @classmethod
    def warning(cls, message: str) -> "ValidationResult":
        """创建警告的验证结果"""
        return cls(is_valid=True, warning_message=message)


class ValidationError(Exception):
    """验证错误异常"""
    def __init__(self, message: str, field_name: str = ""):
        super().__init__(message)
        self.field_name = field_name


class InputValidator(ABC):
    """
    输入验证器基类
    
    定义验证器的基本接口和通用功能
    """
    
    def __init__(self, field_name: str = "", error_message: str = ""):
        self.field_name = field_name
        self.error_message = error_message
    
    @abstractmethod
    def validate(self, value: Any) -> ValidationResult:
        """验证输入值"""
        pass
    
    def __call__(self, value: Any) -> ValidationResult:
        """使验证器可调用"""
        return self.validate(value)


class RequiredFieldValidator(InputValidator):
    """
    必填字段验证器
    
    验证字段是否为空或None
    """
    
    def __init__(self, field_name: str = "", error_message: str = ""):
        super().__init__(field_name, error_message or "此字段为必填项")
    
    def validate(self, value: Any) -> ValidationResult:
        """验证必填字段"""
        if value is None:
            return ValidationResult.error(self.error_message)
        
        if isinstance(value, str) and not value.strip():
            return ValidationResult.error(self.error_message)
        
        if isinstance(value, (list, dict, tuple)) and len(value) == 0:
            return ValidationResult.error(self.error_message)
        
        return ValidationResult.success()


class NumericRangeValidator(InputValidator):
    """
    数字范围验证器
    
    验证数字是否在指定范围内
    """
    
    def __init__(
        self,
        min_value: Optional[Union[int, float]] = None,
        max_value: Optional[Union[int, float]] = None,
        field_name: str = "",
        error_message: str = ""
    ):
        self.min_value = min_value
        self.max_value = max_value
        
        # 生成默认错误消息
        if not error_message:
            if min_value is not None and max_value is not None:
                error_message = f"值必须在 {min_value} 到 {max_value} 之间"
            elif min_value is not None:
                error_message = f"值必须大于等于 {min_value}"
            elif max_value is not None:
                error_message = f"值必须小于等于 {max_value}"
            else:
                error_message = "数值无效"
        
        super().__init__(field_name, error_message)
    
    def validate(self, value: Any) -> ValidationResult:
        """验证数字范围"""
        try:
            # 尝试转换为数字
            if isinstance(value, str):
                if not value.strip():
                    return ValidationResult.error("请输入数值")
                numeric_value = float(value)
            elif isinstance(value, (int, float)):
                numeric_value = float(value)
            else:
                return ValidationResult.error("输入值必须是数字")
            
            # 检查范围
            if self.min_value is not None and numeric_value < self.min_value:
                return ValidationResult.error(self.error_message)
            
            if self.max_value is not None and numeric_value > self.max_value:
                return ValidationResult.error(self.error_message)
            
            # 生成警告（如果接近边界）
            warning_message = ""
            if self.min_value is not None and self.max_value is not None:
                range_size = self.max_value - self.min_value
                if numeric_value <= self.min_value + range_size * 0.1:
                    warning_message = f"值接近下限 ({self.min_value})"
                elif numeric_value >= self.max_value - range_size * 0.1:
                    warning_message = f"值接近上限 ({self.max_value})"
            
            if warning_message:
                return ValidationResult.warning(warning_message)
            
            return ValidationResult.success()
            
        except (ValueError, TypeError):
            return ValidationResult.error("请输入有效的数字")


class IntegerValidator(NumericRangeValidator):
    """
    整数验证器
    
    验证输入是否为整数且在指定范围内
    """
    
    def validate(self, value: Any) -> ValidationResult:
        """验证整数"""
        # 先进行基本数字验证
        result = super().validate(value)
        if not result.is_valid:
            return result
        
        try:
            # 检查是否为整数
            if isinstance(value, str):
                numeric_value = float(value)
            else:
                numeric_value = float(value)
            
            if not numeric_value.is_integer():
                return ValidationResult.error("请输入整数")
            
            return result
            
        except (ValueError, TypeError):
            return ValidationResult.error("请输入有效的整数")


class PercentageValidator(NumericRangeValidator):
    """
    百分比验证器
    
    验证输入是否为有效的百分比值（0-100）
    """
    
    def __init__(self, field_name: str = "", error_message: str = ""):
        super().__init__(
            min_value=0.0,
            max_value=100.0,
            field_name=field_name,
            error_message=error_message or "百分比必须在 0% 到 100% 之间"
        )


class EmailValidator(InputValidator):
    """
    邮箱地址验证器
    
    验证输入是否为有效的邮箱地址格式
    """
    
    def __init__(self, field_name: str = "", error_message: str = ""):
        super().__init__(field_name, error_message or "请输入有效的邮箱地址")
        self.email_pattern = re.compile(
            r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        )
    
    def validate(self, value: Any) -> ValidationResult:
        """验证邮箱地址"""
        if not isinstance(value, str):
            return ValidationResult.error("邮箱地址必须是文本")
        
        if not value.strip():
            return ValidationResult.error("请输入邮箱地址")
        
        if not self.email_pattern.match(value.strip()):
            return ValidationResult.error(self.error_message)
        
        return ValidationResult.success()


class CustomValidator(InputValidator):
    """
    自定义验证器
    
    使用自定义函数进行验证
    """
    
    def __init__(
        self,
        validation_func: Callable[[Any], bool],
        field_name: str = "",
        error_message: str = "输入值无效"
    ):
        super().__init__(field_name, error_message)
        self.validation_func = validation_func
    
    def validate(self, value: Any) -> ValidationResult:
        """使用自定义函数验证"""
        try:
            if self.validation_func(value):
                return ValidationResult.success()
            else:
                return ValidationResult.error(self.error_message)
        except Exception as e:
            return ValidationResult.error(f"验证过程中发生错误: {str(e)}")


class CompositeValidator(InputValidator):
    """
    复合验证器
    
    组合多个验证器，按顺序执行验证
    """
    
    def __init__(self, validators: List[InputValidator], field_name: str = ""):
        super().__init__(field_name)
        self.validators = validators
    
    def validate(self, value: Any) -> ValidationResult:
        """执行所有验证器"""
        warnings = []
        
        for validator in self.validators:
            result = validator.validate(value)
            
            # 如果有错误，立即返回
            if not result.is_valid:
                return result
            
            # 收集警告
            if result.warning_message:
                warnings.append(result.warning_message)
        
        # 如果有警告，返回第一个警告
        if warnings:
            return ValidationResult.warning(warnings[0])
        
        return ValidationResult.success()


class ValidationManager:
    """
    验证管理器
    
    管理多个字段的验证规则和结果
    """
    
    def __init__(self):
        self.validators: dict[str, List[InputValidator]] = {}
        self.validation_results: dict[str, ValidationResult] = {}
    
    def add_validator(self, field_name: str, validator: InputValidator) -> None:
        """添加验证器"""
        if field_name not in self.validators:
            self.validators[field_name] = []
        self.validators[field_name].append(validator)
    
    def validate_field(self, field_name: str, value: Any) -> ValidationResult:
        """验证单个字段"""
        if field_name not in self.validators:
            return ValidationResult.success()
        
        # 创建复合验证器
        composite = CompositeValidator(self.validators[field_name], field_name)
        result = composite.validate(value)
        
        # 保存验证结果
        self.validation_results[field_name] = result
        
        return result
    
    def validate_all(self, data: dict[str, Any]) -> dict[str, ValidationResult]:
        """验证所有字段"""
        results = {}
        
        for field_name, value in data.items():
            results[field_name] = self.validate_field(field_name, value)
        
        return results
    
    def is_valid(self) -> bool:
        """检查所有字段是否都有效"""
        return all(result.is_valid for result in self.validation_results.values())
    
    def get_errors(self) -> dict[str, str]:
        """获取所有错误消息"""
        return {
            field: result.error_message
            for field, result in self.validation_results.items()
            if not result.is_valid
        }
    
    def get_warnings(self) -> dict[str, str]:
        """获取所有警告消息"""
        return {
            field: result.warning_message
            for field, result in self.validation_results.items()
            if result.warning_message
        }
    
    def clear_results(self) -> None:
        """清空验证结果"""
        self.validation_results.clear()


# 预定义的常用验证器
def create_population_size_validator() -> NumericRangeValidator:
    """创建人群规模验证器"""
    return NumericRangeValidator(
        min_value=1,
        max_value=1000000,
        field_name="population_size",
        error_message="人群规模必须在 1 到 1,000,000 之间"
    )


def create_age_validator() -> NumericRangeValidator:
    """创建年龄验证器"""
    return NumericRangeValidator(
        min_value=18,
        max_value=100,
        field_name="age",
        error_message="年龄必须在 18 到 100 岁之间"
    )


def create_probability_validator() -> NumericRangeValidator:
    """创建概率验证器"""
    return NumericRangeValidator(
        min_value=0.0,
        max_value=1.0,
        field_name="probability",
        error_message="概率必须在 0.0 到 1.0 之间"
    )

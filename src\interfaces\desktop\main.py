"""
桌面应用主程序

包含Application类和MainWindow类，提供：
- 应用程序生命周期管理
- 主窗口界面和布局
- 菜单栏和工具栏
- 状态栏和基本样式
"""

import sys
import os
from pathlib import Path
from typing import Optional

from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTabWidget, QMenuBar, QToolBar, QStatusBar, QLabel, QPushButton,
    QMessageBox, QSplitter, QFrame
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QIcon, QAction, QPixmap


class Application(QApplication):
    """
    主应用程序类
    
    管理应用程序生命周期，包括：
    - 应用程序初始化和配置
    - 主窗口创建和管理
    - 全局样式和主题设置
    - 异常处理和错误报告
    """
    
    def __init__(self, argv: list[str]):
        super().__init__(argv)
        
        # 应用程序基本信息
        self.setApplicationName("结直肠癌筛查模拟器")
        self.setApplicationVersion("1.0.0")
        self.setOrganizationName("Medical Simulation Lab")
        self.setOrganizationDomain("medical-sim.org")
        
        # 设置应用程序图标
        self._setup_application_icon()
        
        # 设置应用程序样式
        self._setup_application_style()
        
        # 创建主窗口
        self.main_window: Optional[MainWindow] = None
        
    def _setup_application_icon(self) -> None:
        """设置应用程序图标"""
        # 尝试加载应用程序图标
        icon_path = Path(__file__).parent / "resources" / "icons" / "app_icon.png"
        if icon_path.exists():
            self.setWindowIcon(QIcon(str(icon_path)))
    
    def _setup_application_style(self) -> None:
        """设置应用程序样式"""
        # 基本样式表
        style_sheet = """
        QMainWindow {
            background-color: #f5f5f5;
        }
        
        QTabWidget::pane {
            border: 1px solid #c0c0c0;
            background-color: white;
        }
        
        QTabWidget::tab-bar {
            alignment: left;
        }
        
        QTabBar::tab {
            background-color: #e1e1e1;
            border: 1px solid #c0c0c0;
            padding: 8px 16px;
            margin-right: 2px;
        }
        
        QTabBar::tab:selected {
            background-color: white;
            border-bottom-color: white;
        }
        
        QTabBar::tab:hover {
            background-color: #f0f0f0;
        }
        
        QPushButton {
            background-color: #0078d4;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        QPushButton:hover {
            background-color: #106ebe;
        }
        
        QPushButton:pressed {
            background-color: #005a9e;
        }
        
        QPushButton:disabled {
            background-color: #cccccc;
            color: #666666;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #cccccc;
            border-radius: 5px;
            margin-top: 1ex;
            padding-top: 10px;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        """
        
        self.setStyleSheet(style_sheet)
    
    def create_main_window(self) -> "MainWindow":
        """创建并显示主窗口"""
        if self.main_window is None:
            self.main_window = MainWindow()
        
        self.main_window.show()
        return self.main_window
    
    def run(self) -> int:
        """运行应用程序"""
        try:
            # 创建并显示主窗口
            self.create_main_window()
            
            # 启动事件循环
            return self.exec()
            
        except Exception as e:
            # 显示错误对话框
            QMessageBox.critical(
                None,
                "应用程序错误",
                f"应用程序启动失败：\n{str(e)}"
            )
            return 1


class MainWindow(QMainWindow):
    """
    主窗口类
    
    提供主要的用户界面，包括：
    - 菜单栏和工具栏
    - 标签页界面布局
    - 状态栏和进度显示
    - 窗口管理和事件处理
    """
    
    # 信号定义
    simulation_started = pyqtSignal()
    simulation_stopped = pyqtSignal()
    configuration_changed = pyqtSignal()
    
    def __init__(self, parent: Optional[QWidget] = None):
        super().__init__(parent)
        
        # 窗口基本设置
        self.setWindowTitle("结直肠癌筛查模拟器")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)
        
        # 居中显示窗口
        self._center_window()
        
        # 创建界面组件
        self._create_menu_bar()
        self._create_tool_bar()
        self._create_central_widget()
        self._create_status_bar()
        
        # 连接信号和槽
        self._connect_signals()
        
    def _center_window(self) -> None:
        """将窗口居中显示"""
        screen = self.screen().availableGeometry()
        window = self.frameGeometry()
        window.moveCenter(screen.center())
        self.move(window.topLeft())
    
    def _create_menu_bar(self) -> None:
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")
        
        # 新建项目
        new_action = QAction("新建项目(&N)", self)
        new_action.setShortcut("Ctrl+N")
        new_action.setStatusTip("创建新的模拟项目")
        new_action.triggered.connect(self._new_project)
        file_menu.addAction(new_action)
        
        # 打开项目
        open_action = QAction("打开项目(&O)", self)
        open_action.setShortcut("Ctrl+O")
        open_action.setStatusTip("打开现有的模拟项目")
        open_action.triggered.connect(self._open_project)
        file_menu.addAction(open_action)
        
        file_menu.addSeparator()
        
        # 保存项目
        save_action = QAction("保存项目(&S)", self)
        save_action.setShortcut("Ctrl+S")
        save_action.setStatusTip("保存当前项目")
        save_action.triggered.connect(self._save_project)
        file_menu.addAction(save_action)
        
        # 另存为
        save_as_action = QAction("另存为(&A)", self)
        save_as_action.setShortcut("Ctrl+Shift+S")
        save_as_action.setStatusTip("将项目保存到新位置")
        save_as_action.triggered.connect(self._save_project_as)
        file_menu.addAction(save_as_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction("退出(&X)", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.setStatusTip("退出应用程序")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 编辑菜单
        edit_menu = menubar.addMenu("编辑(&E)")
        
        # 撤销
        undo_action = QAction("撤销(&U)", self)
        undo_action.setShortcut("Ctrl+Z")
        undo_action.setStatusTip("撤销上一个操作")
        undo_action.setEnabled(False)  # 暂时禁用
        edit_menu.addAction(undo_action)
        
        # 重做
        redo_action = QAction("重做(&R)", self)
        redo_action.setShortcut("Ctrl+Y")
        redo_action.setStatusTip("重做上一个操作")
        redo_action.setEnabled(False)  # 暂时禁用
        edit_menu.addAction(redo_action)
        
        # 视图菜单
        view_menu = menubar.addMenu("视图(&V)")
        
        # 全屏
        fullscreen_action = QAction("全屏(&F)", self)
        fullscreen_action.setShortcut("F11")
        fullscreen_action.setStatusTip("切换全屏模式")
        fullscreen_action.triggered.connect(self._toggle_fullscreen)
        view_menu.addAction(fullscreen_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")
        
        # 关于
        about_action = QAction("关于(&A)", self)
        about_action.setStatusTip("显示应用程序信息")
        about_action.triggered.connect(self._show_about)
        help_menu.addAction(about_action)
    
    def _create_tool_bar(self) -> None:
        """创建工具栏"""
        toolbar = self.addToolBar("主工具栏")
        toolbar.setMovable(False)
        
        # 新建按钮
        new_button = QPushButton("新建")
        new_button.setToolTip("创建新项目")
        new_button.clicked.connect(self._new_project)
        toolbar.addWidget(new_button)
        
        # 打开按钮
        open_button = QPushButton("打开")
        open_button.setToolTip("打开项目")
        open_button.clicked.connect(self._open_project)
        toolbar.addWidget(open_button)
        
        # 保存按钮
        save_button = QPushButton("保存")
        save_button.setToolTip("保存项目")
        save_button.clicked.connect(self._save_project)
        toolbar.addWidget(save_button)
        
        toolbar.addSeparator()
        
        # 开始模拟按钮
        self.start_button = QPushButton("开始模拟")
        self.start_button.setToolTip("开始运行模拟")
        self.start_button.clicked.connect(self._start_simulation)
        toolbar.addWidget(self.start_button)
        
        # 停止模拟按钮
        self.stop_button = QPushButton("停止模拟")
        self.stop_button.setToolTip("停止当前模拟")
        self.stop_button.clicked.connect(self._stop_simulation)
        self.stop_button.setEnabled(False)
        toolbar.addWidget(self.stop_button)
    
    def _create_central_widget(self) -> None:
        """创建中央窗口部件"""
        # 导入组件
        from .windows.config_wizard import PopulationConfigWidget
        from .widgets.simulation_control import SimulationControlWidget
        from .windows.results_viewer import ResultsWindow

        # 创建主标签页控件
        self.tab_widget = QTabWidget()
        self.setCentralWidget(self.tab_widget)

        # 配置标签页
        self.config_widget = PopulationConfigWidget()
        self.config_widget.config_changed.connect(self._on_config_changed)
        self.tab_widget.addTab(self.config_widget, "配置")

        # 模拟标签页
        self.simulation_widget = SimulationControlWidget()
        self.simulation_widget.simulation_start_requested.connect(self._on_simulation_start)
        self.simulation_widget.simulation_stop_requested.connect(self._on_simulation_stop)
        self.tab_widget.addTab(self.simulation_widget, "模拟")

        # 结果标签页
        self.results_widget = ResultsWindow()
        self.results_widget.refresh_requested.connect(self._on_results_refresh)
        self.tab_widget.addTab(self.results_widget, "结果")
    
    def _create_status_bar(self) -> None:
        """创建状态栏"""
        self.status_bar = self.statusBar()
        
        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_bar.addWidget(self.status_label)
        
        # 进度标签
        self.progress_label = QLabel("")
        self.status_bar.addPermanentWidget(self.progress_label)
        
        # 显示初始状态
        self.status_bar.showMessage("应用程序已启动", 3000)
    
    def _connect_signals(self) -> None:
        """连接信号和槽"""
        # 标签页切换
        self.tab_widget.currentChanged.connect(self._on_tab_changed)
    
    # 菜单和工具栏事件处理
    def _new_project(self) -> None:
        """新建项目"""
        self.status_label.setText("新建项目")
        self.status_bar.showMessage("创建新项目...", 2000)
    
    def _open_project(self) -> None:
        """打开项目"""
        self.status_label.setText("打开项目")
        self.status_bar.showMessage("打开项目...", 2000)
    
    def _save_project(self) -> None:
        """保存项目"""
        self.status_label.setText("保存项目")
        self.status_bar.showMessage("保存项目...", 2000)
    
    def _save_project_as(self) -> None:
        """另存为项目"""
        self.status_label.setText("另存为")
        self.status_bar.showMessage("另存为项目...", 2000)
    
    def _start_simulation(self) -> None:
        """开始模拟"""
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.status_label.setText("模拟运行中")
        self.status_bar.showMessage("模拟已开始...", 2000)
        self.simulation_started.emit()
    
    def _stop_simulation(self) -> None:
        """停止模拟"""
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.status_label.setText("模拟已停止")
        self.status_bar.showMessage("模拟已停止", 2000)
        self.simulation_stopped.emit()
    
    def _toggle_fullscreen(self) -> None:
        """切换全屏模式"""
        if self.isFullScreen():
            self.showNormal()
        else:
            self.showFullScreen()
    
    def _show_about(self) -> None:
        """显示关于对话框"""
        QMessageBox.about(
            self,
            "关于",
            "结直肠癌筛查模拟器 v1.0.0\n\n"
            "用于评估不同筛查策略成本效益的微观模拟模型\n\n"
            "© 2025 Medical Simulation Lab"
        )
    
    def _on_tab_changed(self, index: int) -> None:
        """标签页切换事件"""
        tab_names = ["配置", "模拟", "结果"]
        if 0 <= index < len(tab_names):
            self.status_bar.showMessage(f"切换到{tab_names[index]}标签页", 1000)

    def _on_config_changed(self, config) -> None:
        """配置变化事件"""
        self.status_bar.showMessage("配置已更新", 2000)
        self.configuration_changed.emit()

    def _on_simulation_start(self, parameters) -> None:
        """模拟开始事件"""
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.status_label.setText("模拟运行中")
        self.status_bar.showMessage(f"模拟已开始 - 参数: {parameters.start_year}年开始", 3000)
        self.simulation_started.emit()

    def _on_simulation_stop(self) -> None:
        """模拟停止事件"""
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.status_label.setText("模拟已停止")
        self.status_bar.showMessage("模拟已停止", 2000)
        self.simulation_stopped.emit()

    def _on_results_refresh(self) -> None:
        """结果刷新事件"""
        self.status_bar.showMessage("刷新结果数据...", 2000)


def main() -> int:
    """主函数"""
    app = Application(sys.argv)
    return app.run()


if __name__ == "__main__":
    sys.exit(main())

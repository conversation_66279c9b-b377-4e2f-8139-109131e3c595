"""
结果显示窗口

提供模拟结果的可视化显示，包括：
- 统计信息表格
- 图表显示
- 数据导出功能
- 结果刷新机制
"""

from typing import Dict, Any, Optional, List
import pandas as pd
from pathlib import Path

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QTableWidget, QTableWidgetItem, QPushButton,
    QGroupBox, QLabel, QTabWidget, QFrame,
    QFileDialog, QMessageBox, QProgressBar,
    QSplitter, QTextEdit, QComboBox
)
from PyQt6.QtCore import Qt, pyqtSignal, QThread, pyqtSlot
from PyQt6.QtGui import QFont

import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import matplotlib
matplotlib.use('Qt5Agg')


class ResultsWindow(QWidget):
    """
    结果显示窗口
    
    提供完整的模拟结果展示界面，包括：
    - 统计数据表格显示
    - 图表可视化
    - 数据导出功能
    - 结果更新和刷新
    """
    
    # 信号定义
    export_requested = pyqtSignal(str, str)  # 格式, 路径
    refresh_requested = pyqtSignal()
    
    def __init__(self, parent: Optional[QWidget] = None):
        super().__init__(parent)
        
        # 结果数据
        self.results_data: Dict[str, Any] = {}
        self.statistics_data: Optional[pd.DataFrame] = None
        
        # 创建界面
        self._create_ui()
        self._connect_signals()
        
        # 初始化空状态
        self._show_empty_state()
    
    def _create_ui(self) -> None:
        """创建用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        # 创建工具栏
        self._create_toolbar(layout)
        
        # 创建主要内容区域
        self._create_main_content(layout)
        
        # 创建状态栏
        self._create_status_bar(layout)
    
    def _create_toolbar(self, parent_layout: QVBoxLayout) -> None:
        """创建工具栏"""
        toolbar_layout = QHBoxLayout()
        
        # 刷新按钮
        self.refresh_button = QPushButton("刷新结果")
        self.refresh_button.clicked.connect(self._refresh_results)
        toolbar_layout.addWidget(self.refresh_button)
        
        toolbar_layout.addStretch()
        
        # 导出选项
        self.export_combo = QComboBox()
        self.export_combo.addItems(["CSV", "Excel", "PDF"])
        toolbar_layout.addWidget(QLabel("导出格式:"))
        toolbar_layout.addWidget(self.export_combo)
        
        # 导出按钮
        self.export_button = QPushButton("导出数据")
        self.export_button.clicked.connect(self._export_data)
        self.export_button.setEnabled(False)
        toolbar_layout.addWidget(self.export_button)
        
        parent_layout.addLayout(toolbar_layout)
    
    def _create_main_content(self, parent_layout: QVBoxLayout) -> None:
        """创建主要内容区域"""
        # 创建标签页
        self.tab_widget = QTabWidget()
        
        # 统计信息标签页
        self._create_statistics_tab()
        
        # 图表标签页
        self._create_charts_tab()
        
        # 详细数据标签页
        self._create_details_tab()
        
        parent_layout.addWidget(self.tab_widget)
    
    def _create_statistics_tab(self) -> None:
        """创建统计信息标签页"""
        stats_widget = QWidget()
        layout = QVBoxLayout(stats_widget)
        
        # 摘要信息组
        summary_group = QGroupBox("模拟摘要")
        summary_layout = QGridLayout(summary_group)
        
        # 基本统计信息标签
        self.population_size_label = QLabel("人群规模: --")
        summary_layout.addWidget(self.population_size_label, 0, 0)
        
        self.simulation_years_label = QLabel("模拟年数: --")
        summary_layout.addWidget(self.simulation_years_label, 0, 1)
        
        self.total_events_label = QLabel("总事件数: --")
        summary_layout.addWidget(self.total_events_label, 1, 0)
        
        self.completion_time_label = QLabel("完成时间: --")
        summary_layout.addWidget(self.completion_time_label, 1, 1)
        
        layout.addWidget(summary_group)
        
        # 详细统计表格
        stats_table_group = QGroupBox("详细统计")
        stats_table_layout = QVBoxLayout(stats_table_group)
        
        self.statistics_table = QTableWidget()
        self.statistics_table.setAlternatingRowColors(True)
        self.statistics_table.setSortingEnabled(True)
        stats_table_layout.addWidget(self.statistics_table)
        
        layout.addWidget(stats_table_group)
        
        self.tab_widget.addTab(stats_widget, "统计信息")
    
    def _create_charts_tab(self) -> None:
        """创建图表标签页"""
        charts_widget = QWidget()
        layout = QVBoxLayout(charts_widget)
        
        # 图表控制面板
        control_layout = QHBoxLayout()
        
        control_layout.addWidget(QLabel("图表类型:"))
        self.chart_type_combo = QComboBox()
        self.chart_type_combo.addItems([
            "年龄分布", "性别分布", "疾病状态分布",
            "生存曲线", "筛查结果", "成本效益"
        ])
        self.chart_type_combo.currentTextChanged.connect(self._update_chart)
        control_layout.addWidget(self.chart_type_combo)
        
        control_layout.addStretch()
        
        # 保存图表按钮
        self.save_chart_button = QPushButton("保存图表")
        self.save_chart_button.clicked.connect(self._save_chart)
        self.save_chart_button.setEnabled(False)
        control_layout.addWidget(self.save_chart_button)
        
        layout.addLayout(control_layout)
        
        # 图表显示区域
        self.figure = Figure(figsize=(10, 6))
        self.canvas = FigureCanvas(self.figure)
        layout.addWidget(self.canvas)
        
        self.tab_widget.addTab(charts_widget, "图表")
    
    def _create_details_tab(self) -> None:
        """创建详细数据标签页"""
        details_widget = QWidget()
        layout = QVBoxLayout(details_widget)
        
        # 数据过滤控制
        filter_layout = QHBoxLayout()
        
        filter_layout.addWidget(QLabel("数据类型:"))
        self.data_type_combo = QComboBox()
        self.data_type_combo.addItems([
            "个体数据", "事件记录", "筛查记录", "成本数据"
        ])
        self.data_type_combo.currentTextChanged.connect(self._update_details_table)
        filter_layout.addWidget(self.data_type_combo)
        
        filter_layout.addStretch()
        
        # 搜索功能
        filter_layout.addWidget(QLabel("搜索:"))
        from PyQt6.QtWidgets import QLineEdit
        self.search_line_edit = QLineEdit()
        self.search_line_edit.setPlaceholderText("输入搜索关键词...")
        self.search_line_edit.textChanged.connect(self._filter_details_table)
        filter_layout.addWidget(self.search_line_edit)
        
        layout.addLayout(filter_layout)
        
        # 详细数据表格
        self.details_table = QTableWidget()
        self.details_table.setAlternatingRowColors(True)
        self.details_table.setSortingEnabled(True)
        layout.addWidget(self.details_table)
        
        self.tab_widget.addTab(details_widget, "详细数据")
    
    def _create_status_bar(self, parent_layout: QVBoxLayout) -> None:
        """创建状态栏"""
        status_layout = QHBoxLayout()
        
        # 状态标签
        self.status_label = QLabel("就绪")
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(200)
        status_layout.addWidget(self.progress_bar)
        
        # 记录数量标签
        self.record_count_label = QLabel("记录数: 0")
        status_layout.addWidget(self.record_count_label)
        
        parent_layout.addLayout(status_layout)
    
    def _connect_signals(self) -> None:
        """连接信号和槽"""
        # 标签页切换
        self.tab_widget.currentChanged.connect(self._on_tab_changed)
    
    def _show_empty_state(self) -> None:
        """显示空状态"""
        self.status_label.setText("无数据")
        self.record_count_label.setText("记录数: 0")
        
        # 清空表格
        self.statistics_table.setRowCount(0)
        self.statistics_table.setColumnCount(0)
        self.details_table.setRowCount(0)
        self.details_table.setColumnCount(0)
        
        # 清空图表
        self.figure.clear()
        self.canvas.draw()
        
        # 禁用按钮
        self.export_button.setEnabled(False)
        self.save_chart_button.setEnabled(False)
    
    def _refresh_results(self) -> None:
        """刷新结果"""
        self.status_label.setText("刷新中...")
        self.refresh_requested.emit()
    
    def _export_data(self) -> None:
        """导出数据"""
        if not self.results_data:
            QMessageBox.warning(self, "警告", "没有可导出的数据")
            return
        
        # 获取导出格式
        export_format = self.export_combo.currentText().lower()
        
        # 选择保存路径
        file_filters = {
            "csv": "CSV文件 (*.csv)",
            "excel": "Excel文件 (*.xlsx)",
            "pdf": "PDF文件 (*.pdf)"
        }
        
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "导出数据",
            f"simulation_results.{export_format}",
            file_filters.get(export_format, "所有文件 (*.*)")
        )
        
        if file_path:
            self.export_requested.emit(export_format, file_path)
    
    def _save_chart(self) -> None:
        """保存图表"""
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "保存图表",
            "chart.png",
            "PNG图片 (*.png);;PDF文件 (*.pdf);;SVG文件 (*.svg)"
        )
        
        if file_path:
            try:
                self.figure.savefig(file_path, dpi=300, bbox_inches='tight')
                QMessageBox.information(self, "成功", f"图表已保存到: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"保存图表失败: {str(e)}")
    
    def _update_chart(self) -> None:
        """更新图表"""
        if not self.results_data:
            return
        
        chart_type = self.chart_type_combo.currentText()
        
        try:
            self.figure.clear()
            ax = self.figure.add_subplot(111)
            
            if chart_type == "年龄分布":
                self._plot_age_distribution(ax)
            elif chart_type == "性别分布":
                self._plot_gender_distribution(ax)
            elif chart_type == "疾病状态分布":
                self._plot_disease_state_distribution(ax)
            elif chart_type == "生存曲线":
                self._plot_survival_curve(ax)
            elif chart_type == "筛查结果":
                self._plot_screening_results(ax)
            elif chart_type == "成本效益":
                self._plot_cost_effectiveness(ax)
            
            self.figure.tight_layout()
            self.canvas.draw()
            self.save_chart_button.setEnabled(True)
            
        except Exception as e:
            ax.text(0.5, 0.5, f"图表生成失败:\n{str(e)}", 
                   ha='center', va='center', transform=ax.transAxes)
            self.canvas.draw()
    
    def _plot_age_distribution(self, ax) -> None:
        """绘制年龄分布图"""
        # 示例数据 - 实际应用中应从results_data获取
        ages = [50, 55, 60, 65, 70, 75, 80]
        counts = [1200, 1500, 1800, 1600, 1400, 1000, 600]
        
        ax.bar(ages, counts, alpha=0.7, color='skyblue')
        ax.set_xlabel('年龄')
        ax.set_ylabel('人数')
        ax.set_title('人群年龄分布')
        ax.grid(True, alpha=0.3)
    
    def _plot_gender_distribution(self, ax) -> None:
        """绘制性别分布图"""
        # 示例数据
        labels = ['男性', '女性']
        sizes = [52, 48]
        colors = ['lightblue', 'lightpink']
        
        ax.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
        ax.set_title('性别分布')
    
    def _plot_disease_state_distribution(self, ax) -> None:
        """绘制疾病状态分布图"""
        # 示例数据
        states = ['正常', '低风险腺瘤', '高风险腺瘤', '临床前癌症', '临床癌症']
        counts = [7500, 1800, 500, 150, 50]
        
        ax.barh(states, counts, color='lightgreen')
        ax.set_xlabel('人数')
        ax.set_title('疾病状态分布')
        ax.grid(True, alpha=0.3)
    
    def _plot_survival_curve(self, ax) -> None:
        """绘制生存曲线"""
        # 示例数据
        years = list(range(0, 21))
        survival_rate = [1.0 - i * 0.02 for i in years]
        
        ax.plot(years, survival_rate, linewidth=2, color='red')
        ax.set_xlabel('年数')
        ax.set_ylabel('生存率')
        ax.set_title('生存曲线')
        ax.grid(True, alpha=0.3)
        ax.set_ylim(0, 1)
    
    def _plot_screening_results(self, ax) -> None:
        """绘制筛查结果图"""
        # 示例数据
        results = ['阴性', '阳性', '假阳性', '假阴性']
        counts = [8500, 1200, 200, 100]
        colors = ['green', 'red', 'orange', 'purple']
        
        ax.bar(results, counts, color=colors, alpha=0.7)
        ax.set_ylabel('人数')
        ax.set_title('筛查结果分布')
        ax.grid(True, alpha=0.3)
    
    def _plot_cost_effectiveness(self, ax) -> None:
        """绘制成本效益图"""
        # 示例数据
        strategies = ['策略A', '策略B', '策略C', '策略D']
        costs = [50000, 75000, 100000, 125000]
        effectiveness = [0.85, 0.90, 0.93, 0.95]
        
        scatter = ax.scatter(costs, effectiveness, s=100, alpha=0.7)
        
        for i, strategy in enumerate(strategies):
            ax.annotate(strategy, (costs[i], effectiveness[i]), 
                       xytext=(5, 5), textcoords='offset points')
        
        ax.set_xlabel('成本 (元)')
        ax.set_ylabel('效果')
        ax.set_title('成本效益分析')
        ax.grid(True, alpha=0.3)
    
    def _update_details_table(self) -> None:
        """更新详细数据表格"""
        data_type = self.data_type_combo.currentText()
        # TODO: 根据数据类型更新表格内容
        self.status_label.setText(f"显示 {data_type}")
    
    def _filter_details_table(self) -> None:
        """过滤详细数据表格"""
        search_text = self.search_line_edit.text()
        # TODO: 实现表格过滤功能
        pass
    
    def _on_tab_changed(self, index: int) -> None:
        """标签页切换事件"""
        tab_names = ["统计信息", "图表", "详细数据"]
        if 0 <= index < len(tab_names):
            self.status_label.setText(f"显示 {tab_names[index]}")
    
    # 公共接口方法
    def update_results(self, results_data: Dict[str, Any]) -> None:
        """更新结果数据"""
        self.results_data = results_data
        
        # 更新摘要信息
        self._update_summary_info()
        
        # 更新统计表格
        self._update_statistics_table()
        
        # 更新图表
        self._update_chart()
        
        # 启用导出按钮
        self.export_button.setEnabled(True)
        
        self.status_label.setText("数据已更新")
    
    def _update_summary_info(self) -> None:
        """更新摘要信息"""
        if 'population_size' in self.results_data:
            self.population_size_label.setText(f"人群规模: {self.results_data['population_size']:,}")
        
        if 'simulation_years' in self.results_data:
            self.simulation_years_label.setText(f"模拟年数: {self.results_data['simulation_years']}")
        
        if 'total_events' in self.results_data:
            self.total_events_label.setText(f"总事件数: {self.results_data['total_events']:,}")
        
        if 'completion_time' in self.results_data:
            self.completion_time_label.setText(f"完成时间: {self.results_data['completion_time']}")
    
    def _update_statistics_table(self) -> None:
        """更新统计表格"""
        if 'statistics' not in self.results_data:
            return
        
        stats = self.results_data['statistics']
        
        # 设置表格结构
        self.statistics_table.setRowCount(len(stats))
        self.statistics_table.setColumnCount(2)
        self.statistics_table.setHorizontalHeaderLabels(['指标', '值'])
        
        # 填充数据
        for row, (key, value) in enumerate(stats.items()):
            self.statistics_table.setItem(row, 0, QTableWidgetItem(str(key)))
            self.statistics_table.setItem(row, 1, QTableWidgetItem(str(value)))
        
        # 调整列宽
        self.statistics_table.resizeColumnsToContents()
        
        self.record_count_label.setText(f"记录数: {len(stats)}")

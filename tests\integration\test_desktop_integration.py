"""
桌面应用集成测试

测试桌面应用各组件之间的集成和交互
"""

import pytest
import sys
from unittest.mock import Mock, patch
from PyQt6.QtWidgets import QApplication
from PyQt6.QtTest import QTest
from PyQt6.QtCore import Qt, QTimer

# 添加src路径
sys.path.insert(0, "src")

from interfaces.desktop.main import Application, MainWindow
from interfaces.desktop.windows.config_wizard import PopulationConfigWidget, PopulationConfig
from interfaces.desktop.widgets.simulation_control import SimulationControlWidget, SimulationParameters
from interfaces.desktop.windows.results_viewer import ResultsWindow


@pytest.fixture(scope="session")
def qapp():
    """创建QApplication实例"""
    if not QApplication.instance():
        app = QApplication([])
        yield app
        app.quit()
    else:
        yield QApplication.instance()


@pytest.fixture
def app(qapp):
    """创建完整的应用程序"""
    application = Application([])
    main_window = application.create_main_window()
    return application, main_window


class TestMainWindowIntegration:
    """测试主窗口集成"""
    
    def test_main_window_components_integration(self, app):
        """测试主窗口组件集成"""
        application, main_window = app
        
        # 检查所有组件是否正确创建
        assert hasattr(main_window, 'config_widget')
        assert hasattr(main_window, 'simulation_widget')
        assert hasattr(main_window, 'results_widget')
        
        assert isinstance(main_window.config_widget, PopulationConfigWidget)
        assert isinstance(main_window.simulation_widget, SimulationControlWidget)
        assert isinstance(main_window.results_widget, ResultsWindow)
    
    def test_tab_widget_integration(self, app):
        """测试标签页组件集成"""
        application, main_window = app
        
        tab_widget = main_window.tab_widget
        
        # 检查标签页数量和内容
        assert tab_widget.count() == 3
        
        # 检查每个标签页的组件
        config_tab = tab_widget.widget(0)
        simulation_tab = tab_widget.widget(1)
        results_tab = tab_widget.widget(2)
        
        assert config_tab is main_window.config_widget
        assert simulation_tab is main_window.simulation_widget
        assert results_tab is main_window.results_widget
    
    def test_signal_connections(self, app):
        """测试信号连接"""
        application, main_window = app
        
        # 测试配置变化信号连接
        with patch.object(main_window, '_on_config_changed') as mock_handler:
            # 模拟配置变化
            config = PopulationConfig(size=20000)
            main_window.config_widget.config_changed.emit(config)
            
            # 检查信号是否被处理
            mock_handler.assert_called_once_with(config)
    
    def test_toolbar_simulation_integration(self, app):
        """测试工具栏与模拟控制集成"""
        application, main_window = app
        
        # 初始状态检查
        assert main_window.start_button.isEnabled()
        assert not main_window.stop_button.isEnabled()
        
        # 模拟开始模拟
        with patch.object(main_window.simulation_widget, 'simulation_start_requested') as mock_signal:
            main_window._start_simulation()
            
            # 检查按钮状态
            assert not main_window.start_button.isEnabled()
            assert main_window.stop_button.isEnabled()
    
    def test_status_bar_updates(self, app):
        """测试状态栏更新"""
        application, main_window = app
        
        # 测试标签页切换状态更新
        tab_widget = main_window.tab_widget
        
        # 切换到模拟标签页
        tab_widget.setCurrentIndex(1)
        main_window._on_tab_changed(1)
        
        # 检查状态栏消息（需要等待消息显示）
        QTest.qWait(100)


class TestConfigSimulationIntegration:
    """测试配置和模拟组件集成"""
    
    def test_config_to_simulation_flow(self, app):
        """测试配置到模拟的流程"""
        application, main_window = app
        
        config_widget = main_window.config_widget
        simulation_widget = main_window.simulation_widget
        
        # 1. 设置人群配置
        config = PopulationConfig(
            size=50000,
            age_mean=65.0,
            male_ratio=0.55
        )
        config_widget.set_config(config)
        
        # 2. 设置模拟参数
        simulation_widget.start_year_spinbox.setValue(2025)
        simulation_widget.duration_spinbox.setValue(30)
        
        # 3. 获取模拟参数
        sim_params = simulation_widget.get_parameters()
        
        # 4. 验证参数传递
        assert sim_params.start_year == 2025
        assert sim_params.duration_years == 30
        
        # 5. 验证配置获取
        retrieved_config = config_widget.get_config()
        assert retrieved_config.size == 50000
        assert retrieved_config.age_mean == 65.0
    
    def test_simulation_parameter_validation(self, app):
        """测试模拟参数验证"""
        application, main_window = app
        
        simulation_widget = main_window.simulation_widget
        
        # 设置有效参数
        simulation_widget.start_year_spinbox.setValue(2025)
        simulation_widget.duration_spinbox.setValue(50)
        simulation_widget.time_step_spinbox.setValue(1.0)
        
        # 获取参数
        params = simulation_widget.get_parameters()
        
        # 验证参数有效性
        assert 2000 <= params.start_year <= 2100
        assert 1 <= params.duration_years <= 100
        assert 0.1 <= params.time_step <= 5.0


class TestSimulationResultsIntegration:
    """测试模拟和结果组件集成"""
    
    def test_simulation_to_results_flow(self, app):
        """测试模拟到结果的流程"""
        application, main_window = app
        
        simulation_widget = main_window.simulation_widget
        results_widget = main_window.results_widget
        
        # 1. 模拟开始
        with patch.object(simulation_widget, 'simulation_start_requested') as mock_start:
            simulation_widget._start_simulation()
            mock_start.emit.assert_called_once()
        
        # 2. 模拟进度更新
        simulation_widget.update_progress(50, 2040, 1000)
        
        # 3. 检查进度显示
        assert simulation_widget.current_progress == 50
        assert simulation_widget.current_year == 2040
        
        # 4. 模拟结果更新
        sample_results = {
            'population_size': 10000,
            'simulation_years': 50,
            'total_events': 50000,
            'completion_time': '2025-01-01 12:00:00',
            'statistics': {
                '总人数': 10000,
                '男性比例': '52%',
                '平均年龄': '60.5岁'
            }
        }
        
        results_widget.update_results(sample_results)
        
        # 5. 验证结果显示
        assert results_widget.results_data == sample_results
        assert results_widget.export_button.isEnabled()
    
    def test_results_refresh_integration(self, app):
        """测试结果刷新集成"""
        application, main_window = app
        
        results_widget = main_window.results_widget
        
        # 模拟刷新请求
        with patch.object(main_window, '_on_results_refresh') as mock_handler:
            results_widget.refresh_requested.emit()
            mock_handler.assert_called_once()


class TestFullWorkflowIntegration:
    """测试完整工作流集成"""
    
    def test_complete_simulation_workflow(self, app):
        """测试完整的模拟工作流"""
        application, main_window = app
        
        config_widget = main_window.config_widget
        simulation_widget = main_window.simulation_widget
        results_widget = main_window.results_widget
        
        # 步骤1: 配置人群参数
        config = PopulationConfig(
            size=10000,
            age_mean=60.0,
            age_std=10.0,
            male_ratio=0.5
        )
        config_widget.set_config(config)
        
        # 步骤2: 配置模拟参数
        simulation_widget.start_year_spinbox.setValue(2025)
        simulation_widget.duration_spinbox.setValue(20)
        simulation_widget.random_seed_spinbox.setValue(42)
        
        # 步骤3: 开始模拟
        with patch.object(simulation_widget, 'simulation_start_requested') as mock_start:
            simulation_widget._start_simulation()
            
            # 验证模拟开始
            mock_start.emit.assert_called_once()
            args = mock_start.emit.call_args[0]
            sim_params = args[0]
            assert sim_params.start_year == 2025
            assert sim_params.duration_years == 20
        
        # 步骤4: 模拟进度更新
        for progress in [25, 50, 75, 100]:
            current_year = 2025 + (progress * 20 // 100)
            simulation_widget.update_progress(progress, current_year, progress * 100)
        
        # 步骤5: 模拟完成，更新结果
        final_results = {
            'population_size': config.size,
            'simulation_years': 20,
            'total_events': 20000,
            'statistics': {
                '模拟完成': '是',
                '最终年份': '2045'
            }
        }
        
        results_widget.update_results(final_results)
        
        # 步骤6: 验证最终状态
        assert simulation_widget.current_progress == 100
        assert results_widget.results_data == final_results
        assert results_widget.export_button.isEnabled()
    
    def test_error_handling_integration(self, app):
        """测试错误处理集成"""
        application, main_window = app
        
        simulation_widget = main_window.simulation_widget
        
        # 模拟错误状态
        from interfaces.desktop.widgets.simulation_control import SimulationStatus
        simulation_widget.set_status(SimulationStatus.ERROR)
        
        # 检查UI状态
        assert simulation_widget.start_button.isEnabled()
        assert simulation_widget.start_button.text() == "重新开始"
        assert not simulation_widget.stop_button.isEnabled()
    
    def test_data_export_integration(self, app):
        """测试数据导出集成"""
        application, main_window = app
        
        results_widget = main_window.results_widget
        
        # 设置结果数据
        sample_data = {
            'population_size': 10000,
            'statistics': {'test': 'data'}
        }
        results_widget.update_results(sample_data)
        
        # 测试导出请求
        with patch.object(results_widget, 'export_requested') as mock_export:
            # 模拟用户选择导出格式和路径
            results_widget.export_combo.setCurrentText("CSV")
            
            # 模拟文件对话框返回路径
            with patch('PyQt6.QtWidgets.QFileDialog.getSaveFileName') as mock_dialog:
                mock_dialog.return_value = ("test_export.csv", "CSV文件 (*.csv)")
                
                results_widget._export_data()
                
                # 验证导出信号
                mock_export.emit.assert_called_once_with("csv", "test_export.csv")


class TestPerformanceIntegration:
    """测试性能集成"""
    
    def test_large_config_handling(self, app):
        """测试大规模配置处理"""
        application, main_window = app
        
        config_widget = main_window.config_widget
        
        # 设置大规模配置
        large_config = PopulationConfig(size=100000)
        
        # 测试设置大配置的响应时间
        import time
        start_time = time.time()
        
        config_widget.set_config(large_config)
        config_widget._generate_preview()
        
        end_time = time.time()
        
        # 验证响应时间合理（应该小于1秒）
        assert (end_time - start_time) < 1.0
        
        # 验证配置正确设置
        assert config_widget.config.size == 100000
    
    def test_ui_responsiveness(self, app):
        """测试UI响应性"""
        application, main_window = app
        
        # 快速切换标签页
        tab_widget = main_window.tab_widget
        
        for i in range(10):  # 快速切换10次
            tab_widget.setCurrentIndex(i % 3)
            QTest.qWait(10)  # 短暂等待
        
        # 验证最终状态正确
        assert 0 <= tab_widget.currentIndex() < 3


if __name__ == "__main__":
    pytest.main([__file__])

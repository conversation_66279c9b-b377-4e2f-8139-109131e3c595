"""
人群配置向导

提供图形化的人群配置界面，包括：
- 人群规模设置
- 年龄分布配置
- 性别比例设置
- 配置预览和验证
"""

from typing import Dict, Any, Optional
from dataclasses import dataclass

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QGroupBox, QLabel, QSpinBox, QDoubleSpinBox, QSlider,
    QComboBox, QPushButton, QTextEdit, QProgressBar,
    QFormLayout, QFrame, QScrollArea
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont


@dataclass
class PopulationConfig:
    """人群配置数据类"""
    size: int = 10000
    age_mean: float = 60.0
    age_std: float = 10.0
    age_min: int = 40
    age_max: int = 80
    male_ratio: float = 0.5
    distribution_type: str = "normal"


class PopulationConfigWidget(QWidget):
    """
    人群配置界面组件
    
    提供用户友好的人群参数配置界面，包括：
    - 人群规模输入控件
    - 年龄分布参数设置
    - 性别比例调节
    - 实时预览和验证
    """
    
    # 信号定义
    config_changed = pyqtSignal(PopulationConfig)
    preview_requested = pyqtSignal()
    
    def __init__(self, parent: Optional[QWidget] = None):
        super().__init__(parent)
        
        # 配置数据
        self.config = PopulationConfig()
        
        # 创建界面
        self._create_ui()
        self._connect_signals()
        
        # 初始化界面值
        self._update_ui_from_config()
    
    def _create_ui(self) -> None:
        """创建用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        # 人群规模配置组
        self._create_population_size_group(scroll_layout)
        
        # 年龄分布配置组
        self._create_age_distribution_group(scroll_layout)
        
        # 性别分布配置组
        self._create_gender_distribution_group(scroll_layout)
        
        # 预览和控制组
        self._create_preview_group(scroll_layout)
        
        # 设置滚动区域
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)
        
        # 底部按钮组
        self._create_button_group(layout)
    
    def _create_population_size_group(self, parent_layout: QVBoxLayout) -> None:
        """创建人群规模配置组"""
        group = QGroupBox("人群规模设置")
        layout = QFormLayout(group)
        
        # 人群规模输入
        self.size_spinbox = QSpinBox()
        self.size_spinbox.setRange(100, 1000000)
        self.size_spinbox.setValue(10000)
        self.size_spinbox.setSingleStep(1000)
        self.size_spinbox.setSuffix(" 人")
        layout.addRow("人群规模:", self.size_spinbox)
        
        # 添加说明
        info_label = QLabel("建议范围：1,000 - 100,000 人")
        info_label.setStyleSheet("color: #666666; font-size: 12px;")
        layout.addRow("", info_label)
        
        parent_layout.addWidget(group)
    
    def _create_age_distribution_group(self, parent_layout: QVBoxLayout) -> None:
        """创建年龄分布配置组"""
        group = QGroupBox("年龄分布设置")
        layout = QFormLayout(group)
        
        # 分布类型选择
        self.distribution_combo = QComboBox()
        self.distribution_combo.addItems(["正态分布", "均匀分布", "自定义分布"])
        layout.addRow("分布类型:", self.distribution_combo)
        
        # 年龄范围
        age_range_layout = QHBoxLayout()
        
        self.age_min_spinbox = QSpinBox()
        self.age_min_spinbox.setRange(18, 100)
        self.age_min_spinbox.setValue(40)
        self.age_min_spinbox.setSuffix(" 岁")
        age_range_layout.addWidget(self.age_min_spinbox)
        
        age_range_layout.addWidget(QLabel("至"))
        
        self.age_max_spinbox = QSpinBox()
        self.age_max_spinbox.setRange(18, 100)
        self.age_max_spinbox.setValue(80)
        self.age_max_spinbox.setSuffix(" 岁")
        age_range_layout.addWidget(self.age_max_spinbox)
        
        layout.addRow("年龄范围:", age_range_layout)
        
        # 年龄均值
        self.age_mean_spinbox = QDoubleSpinBox()
        self.age_mean_spinbox.setRange(18.0, 100.0)
        self.age_mean_spinbox.setValue(60.0)
        self.age_mean_spinbox.setSingleStep(0.5)
        self.age_mean_spinbox.setSuffix(" 岁")
        layout.addRow("年龄均值:", self.age_mean_spinbox)
        
        # 年龄标准差
        self.age_std_spinbox = QDoubleSpinBox()
        self.age_std_spinbox.setRange(1.0, 30.0)
        self.age_std_spinbox.setValue(10.0)
        self.age_std_spinbox.setSingleStep(0.5)
        self.age_std_spinbox.setSuffix(" 岁")
        layout.addRow("标准差:", self.age_std_spinbox)
        
        parent_layout.addWidget(group)
    
    def _create_gender_distribution_group(self, parent_layout: QVBoxLayout) -> None:
        """创建性别分布配置组"""
        group = QGroupBox("性别分布设置")
        layout = QVBoxLayout(group)
        
        # 性别比例滑块
        slider_layout = QHBoxLayout()
        
        slider_layout.addWidget(QLabel("女性"))
        
        self.gender_slider = QSlider(Qt.Orientation.Horizontal)
        self.gender_slider.setRange(0, 100)
        self.gender_slider.setValue(50)
        self.gender_slider.setTickPosition(QSlider.TickPosition.TicksBelow)
        self.gender_slider.setTickInterval(10)
        slider_layout.addWidget(self.gender_slider)
        
        slider_layout.addWidget(QLabel("男性"))
        
        layout.addLayout(slider_layout)
        
        # 比例显示
        ratio_layout = QHBoxLayout()
        
        self.female_ratio_label = QLabel("女性: 50.0%")
        self.female_ratio_label.setAlignment(Qt.AlignmentFlag.AlignLeft)
        ratio_layout.addWidget(self.female_ratio_label)
        
        self.male_ratio_label = QLabel("男性: 50.0%")
        self.male_ratio_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        ratio_layout.addWidget(self.male_ratio_label)
        
        layout.addLayout(ratio_layout)
        
        parent_layout.addWidget(group)
    
    def _create_preview_group(self, parent_layout: QVBoxLayout) -> None:
        """创建预览配置组"""
        group = QGroupBox("配置预览")
        layout = QVBoxLayout(group)
        
        # 配置摘要文本
        self.preview_text = QTextEdit()
        self.preview_text.setMaximumHeight(120)
        self.preview_text.setReadOnly(True)
        layout.addWidget(self.preview_text)
        
        # 生成预览按钮
        self.preview_button = QPushButton("生成预览")
        self.preview_button.clicked.connect(self._generate_preview)
        layout.addWidget(self.preview_button)
        
        parent_layout.addWidget(group)
    
    def _create_button_group(self, parent_layout: QVBoxLayout) -> None:
        """创建底部按钮组"""
        button_layout = QHBoxLayout()
        
        # 重置按钮
        self.reset_button = QPushButton("重置")
        self.reset_button.clicked.connect(self._reset_config)
        button_layout.addWidget(self.reset_button)
        
        button_layout.addStretch()
        
        # 应用按钮
        self.apply_button = QPushButton("应用配置")
        self.apply_button.clicked.connect(self._apply_config)
        button_layout.addWidget(self.apply_button)
        
        parent_layout.addLayout(button_layout)
    
    def _connect_signals(self) -> None:
        """连接信号和槽"""
        # 数值变化信号
        self.size_spinbox.valueChanged.connect(self._on_config_changed)
        self.age_min_spinbox.valueChanged.connect(self._on_config_changed)
        self.age_max_spinbox.valueChanged.connect(self._on_config_changed)
        self.age_mean_spinbox.valueChanged.connect(self._on_config_changed)
        self.age_std_spinbox.valueChanged.connect(self._on_config_changed)
        self.gender_slider.valueChanged.connect(self._on_gender_ratio_changed)
        self.distribution_combo.currentTextChanged.connect(self._on_config_changed)
    
    def _update_ui_from_config(self) -> None:
        """从配置更新界面"""
        self.size_spinbox.setValue(self.config.size)
        self.age_min_spinbox.setValue(self.config.age_min)
        self.age_max_spinbox.setValue(self.config.age_max)
        self.age_mean_spinbox.setValue(self.config.age_mean)
        self.age_std_spinbox.setValue(self.config.age_std)
        self.gender_slider.setValue(int(self.config.male_ratio * 100))
        
        # 更新分布类型
        distribution_map = {"normal": "正态分布", "uniform": "均匀分布", "custom": "自定义分布"}
        display_name = distribution_map.get(self.config.distribution_type, "正态分布")
        index = self.distribution_combo.findText(display_name)
        if index >= 0:
            self.distribution_combo.setCurrentIndex(index)
    
    def _update_config_from_ui(self) -> None:
        """从界面更新配置"""
        self.config.size = self.size_spinbox.value()
        self.config.age_min = self.age_min_spinbox.value()
        self.config.age_max = self.age_max_spinbox.value()
        self.config.age_mean = self.age_mean_spinbox.value()
        self.config.age_std = self.age_std_spinbox.value()
        self.config.male_ratio = self.gender_slider.value() / 100.0
        
        # 更新分布类型
        distribution_map = {"正态分布": "normal", "均匀分布": "uniform", "自定义分布": "custom"}
        self.config.distribution_type = distribution_map.get(
            self.distribution_combo.currentText(), "normal"
        )
    
    def _on_config_changed(self) -> None:
        """配置变化处理"""
        self._update_config_from_ui()
        self._generate_preview()
        self.config_changed.emit(self.config)
    
    def _on_gender_ratio_changed(self, value: int) -> None:
        """性别比例变化处理"""
        male_ratio = value / 100.0
        female_ratio = 1.0 - male_ratio
        
        self.male_ratio_label.setText(f"男性: {male_ratio:.1%}")
        self.female_ratio_label.setText(f"女性: {female_ratio:.1%}")
        
        self._on_config_changed()
    
    def _generate_preview(self) -> None:
        """生成配置预览"""
        preview_text = f"""
配置摘要：

人群规模: {self.config.size:,} 人
年龄分布: {self.config.distribution_type} ({self.config.age_min}-{self.config.age_max}岁)
年龄均值: {self.config.age_mean:.1f}岁
标准差: {self.config.age_std:.1f}岁
性别比例: 男性 {self.config.male_ratio:.1%}, 女性 {1-self.config.male_ratio:.1%}

预计内存使用: {self.config.size * 0.001:.1f} MB
预计生成时间: {self.config.size / 10000:.1f} 秒
        """.strip()
        
        self.preview_text.setPlainText(preview_text)
        self.preview_requested.emit()
    
    def _reset_config(self) -> None:
        """重置配置"""
        self.config = PopulationConfig()
        self._update_ui_from_config()
        self._generate_preview()
    
    def _apply_config(self) -> None:
        """应用配置"""
        self._update_config_from_ui()
        self.config_changed.emit(self.config)
    
    def get_config(self) -> PopulationConfig:
        """获取当前配置"""
        self._update_config_from_ui()
        return self.config
    
    def set_config(self, config: PopulationConfig) -> None:
        """设置配置"""
        self.config = config
        self._update_ui_from_config()
        self._generate_preview()

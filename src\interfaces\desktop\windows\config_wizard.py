"""
人群配置向导

提供图形化的人群配置界面，包括：
- 人群规模设置
- 年龄分布配置
- 性别比例设置
- 配置预览和验证
"""

from typing import Dict, Any, Optional
from dataclasses import dataclass

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QGroupBox, QLabel, QSpinBox, QDoubleSpinBox, QSlider,
    QComboBox, QPushButton, QTextEdit, QProgressBar,
    QFormLayout, QFrame, QScrollArea, QFileDialog, QMessageBox
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont

import pandas as pd
import numpy as np
from pathlib import Path


@dataclass
class PopulationConfig:
    """人群配置数据类"""
    size: int = 10000
    age_mean: float = 60.0
    age_std: float = 10.0
    age_min: int = 40
    age_max: int = 80
    male_ratio: float = 0.5
    distribution_type: str = "normal"
    # 新增：文件导入相关字段
    imported_file_path: str = ""
    use_imported_data: bool = False
    imported_age_distribution: Optional[Dict[int, int]] = None  # 年龄->人数
    imported_gender_distribution: Optional[Dict[str, int]] = None  # 性别->人数


class PopulationConfigWidget(QWidget):
    """
    人群配置界面组件
    
    提供用户友好的人群参数配置界面，包括：
    - 人群规模输入控件
    - 年龄分布参数设置
    - 性别比例调节
    - 实时预览和验证
    """
    
    # 信号定义
    config_changed = pyqtSignal(PopulationConfig)
    preview_requested = pyqtSignal()
    
    def __init__(self, parent: Optional[QWidget] = None):
        super().__init__(parent)
        
        # 配置数据
        self.config = PopulationConfig()
        
        # 创建界面
        self._create_ui()
        self._connect_signals()
        
        # 初始化界面值
        self._update_ui_from_config()
    
    def _create_ui(self) -> None:
        """创建用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)

        # 文件导入配置组
        self._create_file_import_group(scroll_layout)

        # 人群规模配置组
        self._create_population_size_group(scroll_layout)

        # 年龄分布配置组
        self._create_age_distribution_group(scroll_layout)

        # 性别分布配置组
        self._create_gender_distribution_group(scroll_layout)
        
        # 预览和控制组
        self._create_preview_group(scroll_layout)
        
        # 设置滚动区域
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)
        
        # 底部按钮组
        self._create_button_group(layout)
    
    def _create_population_size_group(self, parent_layout: QVBoxLayout) -> None:
        """创建人群规模配置组"""
        group = QGroupBox("人群规模设置")
        layout = QFormLayout(group)
        
        # 人群规模输入
        self.size_spinbox = QSpinBox()
        self.size_spinbox.setRange(100, 1000000)
        self.size_spinbox.setValue(10000)
        self.size_spinbox.setSingleStep(1000)
        self.size_spinbox.setSuffix(" 人")
        layout.addRow("人群规模:", self.size_spinbox)
        
        # 添加说明
        info_label = QLabel("建议范围：1,000 - 100,000 人")
        info_label.setStyleSheet("color: #666666; font-size: 12px;")
        layout.addRow("", info_label)
        
        parent_layout.addWidget(group)
    
    def _create_age_distribution_group(self, parent_layout: QVBoxLayout) -> None:
        """创建年龄分布配置组"""
        group = QGroupBox("年龄分布设置")
        layout = QFormLayout(group)
        
        # 分布类型选择
        self.distribution_combo = QComboBox()
        self.distribution_combo.addItems(["正态分布", "均匀分布", "自定义分布"])
        layout.addRow("分布类型:", self.distribution_combo)
        
        # 年龄范围
        age_range_layout = QHBoxLayout()
        
        self.age_min_spinbox = QSpinBox()
        self.age_min_spinbox.setRange(18, 100)
        self.age_min_spinbox.setValue(40)
        self.age_min_spinbox.setSuffix(" 岁")
        age_range_layout.addWidget(self.age_min_spinbox)
        
        age_range_layout.addWidget(QLabel("至"))
        
        self.age_max_spinbox = QSpinBox()
        self.age_max_spinbox.setRange(18, 100)
        self.age_max_spinbox.setValue(80)
        self.age_max_spinbox.setSuffix(" 岁")
        age_range_layout.addWidget(self.age_max_spinbox)
        
        layout.addRow("年龄范围:", age_range_layout)
        
        # 年龄均值
        self.age_mean_spinbox = QDoubleSpinBox()
        self.age_mean_spinbox.setRange(18.0, 100.0)
        self.age_mean_spinbox.setValue(60.0)
        self.age_mean_spinbox.setSingleStep(0.5)
        self.age_mean_spinbox.setSuffix(" 岁")
        layout.addRow("年龄均值:", self.age_mean_spinbox)
        
        # 年龄标准差
        self.age_std_spinbox = QDoubleSpinBox()
        self.age_std_spinbox.setRange(1.0, 30.0)
        self.age_std_spinbox.setValue(10.0)
        self.age_std_spinbox.setSingleStep(0.5)
        self.age_std_spinbox.setSuffix(" 岁")
        layout.addRow("标准差:", self.age_std_spinbox)
        
        parent_layout.addWidget(group)
    
    def _create_gender_distribution_group(self, parent_layout: QVBoxLayout) -> None:
        """创建性别分布配置组"""
        group = QGroupBox("性别分布设置")
        layout = QVBoxLayout(group)
        
        # 性别比例滑块
        slider_layout = QHBoxLayout()
        
        slider_layout.addWidget(QLabel("女性"))
        
        self.gender_slider = QSlider(Qt.Orientation.Horizontal)
        self.gender_slider.setRange(0, 100)
        self.gender_slider.setValue(50)
        self.gender_slider.setTickPosition(QSlider.TickPosition.TicksBelow)
        self.gender_slider.setTickInterval(10)
        slider_layout.addWidget(self.gender_slider)
        
        slider_layout.addWidget(QLabel("男性"))
        
        layout.addLayout(slider_layout)
        
        # 比例显示
        ratio_layout = QHBoxLayout()
        
        self.female_ratio_label = QLabel("女性: 50.0%")
        self.female_ratio_label.setAlignment(Qt.AlignmentFlag.AlignLeft)
        ratio_layout.addWidget(self.female_ratio_label)
        
        self.male_ratio_label = QLabel("男性: 50.0%")
        self.male_ratio_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        ratio_layout.addWidget(self.male_ratio_label)
        
        layout.addLayout(ratio_layout)
        
        parent_layout.addWidget(group)
    
    def _create_preview_group(self, parent_layout: QVBoxLayout) -> None:
        """创建预览配置组"""
        group = QGroupBox("配置预览")
        layout = QVBoxLayout(group)
        
        # 配置摘要文本
        self.preview_text = QTextEdit()
        self.preview_text.setMaximumHeight(120)
        self.preview_text.setReadOnly(True)
        layout.addWidget(self.preview_text)
        
        # 生成预览按钮
        self.preview_button = QPushButton("生成预览")
        self.preview_button.clicked.connect(self._generate_preview)
        layout.addWidget(self.preview_button)
        
        parent_layout.addWidget(group)
    
    def _create_button_group(self, parent_layout: QVBoxLayout) -> None:
        """创建底部按钮组"""
        button_layout = QHBoxLayout()
        
        # 重置按钮
        self.reset_button = QPushButton("重置")
        self.reset_button.clicked.connect(self._reset_config)
        button_layout.addWidget(self.reset_button)
        
        button_layout.addStretch()
        
        # 应用按钮
        self.apply_button = QPushButton("应用配置")
        self.apply_button.clicked.connect(self._apply_config)
        button_layout.addWidget(self.apply_button)
        
        parent_layout.addLayout(button_layout)

    def _create_file_import_group(self, parent_layout: QVBoxLayout) -> None:
        """创建文件导入配置组"""
        group = QGroupBox("人群结构文件导入")
        layout = QVBoxLayout(group)

        # 文件选择区域
        file_layout = QHBoxLayout()

        self.file_path_label = QLabel("未选择文件")
        self.file_path_label.setStyleSheet("color: #666666; font-style: italic;")
        file_layout.addWidget(self.file_path_label)

        file_layout.addStretch()

        self.browse_file_button = QPushButton("浏览文件")
        self.browse_file_button.clicked.connect(self._browse_population_file)
        file_layout.addWidget(self.browse_file_button)

        layout.addLayout(file_layout)

        # 使用导入数据选项
        self.use_imported_checkbox = QComboBox()
        self.use_imported_checkbox.addItems(["使用手动配置", "使用导入文件数据"])
        self.use_imported_checkbox.currentTextChanged.connect(self._on_import_mode_changed)
        layout.addWidget(QLabel("数据来源:"))
        layout.addWidget(self.use_imported_checkbox)

        # 导入数据预览
        self.import_preview_text = QTextEdit()
        self.import_preview_text.setMaximumHeight(100)
        self.import_preview_text.setReadOnly(True)
        self.import_preview_text.setPlaceholderText("导入文件后将在此显示数据预览...")
        layout.addWidget(QLabel("数据预览:"))
        layout.addWidget(self.import_preview_text)

        # 文件格式说明
        info_label = QLabel(
            "支持格式: Excel (.xlsx, .xls) 和 CSV (.csv)\n"
            "要求列: age (年龄), gender (性别: M/F 或 男/女), number (人数：number/count)\n"
        )
        info_label.setStyleSheet("color: #666666; font-size: 12px;")
        layout.addWidget(info_label)

        parent_layout.addWidget(group)

    def _connect_signals(self) -> None:
        """连接信号和槽"""
        # 数值变化信号
        self.size_spinbox.valueChanged.connect(self._on_config_changed)
        self.age_min_spinbox.valueChanged.connect(self._on_config_changed)
        self.age_max_spinbox.valueChanged.connect(self._on_config_changed)
        self.age_mean_spinbox.valueChanged.connect(self._on_config_changed)
        self.age_std_spinbox.valueChanged.connect(self._on_config_changed)
        self.gender_slider.valueChanged.connect(self._on_gender_ratio_changed)
        self.distribution_combo.currentTextChanged.connect(self._on_config_changed)

        # 文件导入信号
        self.use_imported_checkbox.currentTextChanged.connect(self._on_import_mode_changed)
    
    def _update_ui_from_config(self) -> None:
        """从配置更新界面"""
        self.size_spinbox.setValue(self.config.size)
        self.age_min_spinbox.setValue(self.config.age_min)
        self.age_max_spinbox.setValue(self.config.age_max)
        self.age_mean_spinbox.setValue(self.config.age_mean)
        self.age_std_spinbox.setValue(self.config.age_std)
        self.gender_slider.setValue(int(self.config.male_ratio * 100))

        # 更新分布类型
        distribution_map = {"normal": "正态分布", "uniform": "均匀分布", "custom": "自定义分布", "imported": "导入数据"}
        display_name = distribution_map.get(self.config.distribution_type, "正态分布")
        index = self.distribution_combo.findText(display_name)
        if index >= 0:
            self.distribution_combo.setCurrentIndex(index)

        # 更新文件导入相关UI
        if self.config.imported_file_path:
            file_name = Path(self.config.imported_file_path).name
            self.file_path_label.setText(f"已选择: {file_name}")
            self.file_path_label.setStyleSheet("color: #2e7d32; font-weight: bold;")

        # 更新导入模式
        import_mode = "使用导入文件数据" if self.config.use_imported_data else "使用手动配置"
        self.use_imported_checkbox.setCurrentText(import_mode)

        # 如果有导入数据，更新预览
        if self.config.use_imported_data and self.config.imported_age_distribution:
            # 创建临时DataFrame用于预览
            age_dist = self.config.imported_age_distribution
            gender_dist = self.config.imported_gender_distribution
            self._update_import_preview_from_dists(age_dist, gender_dist)

    def _update_import_preview_from_dists(
        self,
        age_dist: Dict[int, int],
        gender_dist: Dict[str, int]
    ) -> None:
        """从分布字典更新导入预览"""
        total_records = sum(age_dist.values())
        age_range = f"{min(age_dist.keys())}-{max(age_dist.keys())}"
        male_count = gender_dist.get('Male', 0)
        female_count = gender_dist.get('Female', 0)
        male_ratio = male_count / (male_count + female_count) * 100 if (male_count + female_count) > 0 else 0

        # 计算平均年龄
        total_age_sum = sum(age * count for age, count in age_dist.items())
        avg_age = total_age_sum / total_records if total_records > 0 else 0

        preview_text = f"""
数据概览:
• 总记录数: {total_records:,}
• 年龄范围: {age_range}岁
• 性别分布: 男性 {male_count:,} ({male_ratio:.1f}%), 女性 {female_count:,} ({100-male_ratio:.1f}%)
• 平均年龄: {avg_age:.1f}岁

年龄分布 (前10个):
{self._format_age_distribution_preview(age_dist)}
        """.strip()

        self.import_preview_text.setPlainText(preview_text)
    
    def _update_config_from_ui(self) -> None:
        """从界面更新配置"""
        self.config.size = self.size_spinbox.value()
        self.config.age_min = self.age_min_spinbox.value()
        self.config.age_max = self.age_max_spinbox.value()
        self.config.age_mean = self.age_mean_spinbox.value()
        self.config.age_std = self.age_std_spinbox.value()
        self.config.male_ratio = self.gender_slider.value() / 100.0
        
        # 更新分布类型
        distribution_map = {"正态分布": "normal", "均匀分布": "uniform", "自定义分布": "custom"}
        self.config.distribution_type = distribution_map.get(
            self.distribution_combo.currentText(), "normal"
        )
    
    def _on_config_changed(self) -> None:
        """配置变化处理"""
        self._update_config_from_ui()
        self._generate_preview()
        self.config_changed.emit(self.config)
    
    def _on_gender_ratio_changed(self, value: int) -> None:
        """性别比例变化处理"""
        male_ratio = value / 100.0
        female_ratio = 1.0 - male_ratio
        
        self.male_ratio_label.setText(f"男性: {male_ratio:.1%}")
        self.female_ratio_label.setText(f"女性: {female_ratio:.1%}")
        
        self._on_config_changed()
    
    def _generate_preview(self) -> None:
        """生成配置预览"""
        if self.config.use_imported_data and self.config.imported_age_distribution:
            # 使用导入数据的预览
            age_dist = self.config.imported_age_distribution
            gender_dist = self.config.imported_gender_distribution

            preview_text = f"""
配置摘要（基于导入文件）：

数据来源: {Path(self.config.imported_file_path).name if self.config.imported_file_path else '导入文件'}
人群规模: {self.config.size:,} 人
年龄范围: {self.config.age_min}-{self.config.age_max}岁
年龄均值: {self.config.age_mean:.1f}岁
标准差: {self.config.age_std:.1f}岁
性别比例: 男性 {self.config.male_ratio:.1%}, 女性 {1-self.config.male_ratio:.1%}

年龄分布类型: 实际数据分布
独特年龄数: {len(age_dist)}个
性别分布: {', '.join([f'{k}: {v:,}人' for k, v in gender_dist.items()])}

预计内存使用: {self.config.size * 0.001:.1f} MB
预计生成时间: {self.config.size / 10000:.1f} 秒
            """.strip()
        else:
            # 使用手动配置的预览
            preview_text = f"""
配置摘要（手动配置）：

人群规模: {self.config.size:,} 人
年龄分布: {self.config.distribution_type} ({self.config.age_min}-{self.config.age_max}岁)
年龄均值: {self.config.age_mean:.1f}岁
标准差: {self.config.age_std:.1f}岁
性别比例: 男性 {self.config.male_ratio:.1%}, 女性 {1-self.config.male_ratio:.1%}

预计内存使用: {self.config.size * 0.001:.1f} MB
预计生成时间: {self.config.size / 10000:.1f} 秒
            """.strip()

        self.preview_text.setPlainText(preview_text)
        self.preview_requested.emit()
    
    def _reset_config(self) -> None:
        """重置配置"""
        self.config = PopulationConfig()
        self._update_ui_from_config()
        self._generate_preview()
    
    def _apply_config(self) -> None:
        """应用配置"""
        self._update_config_from_ui()
        self.config_changed.emit(self.config)
    
    def get_config(self) -> PopulationConfig:
        """获取当前配置"""
        self._update_config_from_ui()
        return self.config
    
    def set_config(self, config: PopulationConfig) -> None:
        """设置配置"""
        self.config = config
        self._update_ui_from_config()
        self._generate_preview()

    def _browse_population_file(self) -> None:
        """浏览人群结构文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择人群结构文件",
            "",
            "Excel文件 (*.xlsx *.xls);;CSV文件 (*.csv);;所有文件 (*.*)"
        )

        if file_path:
            try:
                self._load_population_file(file_path)
            except Exception as e:
                QMessageBox.critical(
                    self,
                    "文件加载错误",
                    f"无法加载文件: {file_path}\n\n错误信息: {str(e)}"
                )

    def _load_population_file(self, file_path: str) -> None:
        """加载人群结构文件"""
        file_path_obj = Path(file_path)

        # 根据文件扩展名选择加载方法
        if file_path_obj.suffix.lower() in ['.xlsx', '.xls']:
            df = pd.read_excel(file_path)
        elif file_path_obj.suffix.lower() == '.csv':
            # 尝试不同的编码
            for encoding in ['utf-8', 'gbk', 'gb2312']:
                try:
                    df = pd.read_csv(file_path, encoding=encoding)
                    break
                except UnicodeDecodeError:
                    continue
            else:
                raise ValueError("无法识别文件编码，请确保文件为UTF-8、GBK或GB2312编码")
        else:
            raise ValueError("不支持的文件格式，请选择Excel或CSV文件")

        # 验证必需的列
        required_columns = self._validate_file_columns(df)

        # 处理数据
        age_distribution, gender_distribution = self._process_population_data(df, required_columns)

        # 更新配置
        self.config.imported_file_path = file_path
        self.config.imported_age_distribution = age_distribution
        self.config.imported_gender_distribution = gender_distribution

        # 更新界面
        self.file_path_label.setText(f"已选择: {file_path_obj.name}")
        self.file_path_label.setStyleSheet("color: #2e7d32; font-weight: bold;")

        # 生成预览
        self._update_import_preview(df, age_distribution, gender_distribution)

        # 提示用户切换到导入模式
        if self.use_imported_checkbox.currentText() == "使用手动配置":
            reply = QMessageBox.question(
                self,
                "切换数据来源",
                "文件加载成功！是否切换到使用导入文件数据？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            if reply == QMessageBox.StandardButton.Yes:
                self.use_imported_checkbox.setCurrentText("使用导入文件数据")

    def _validate_file_columns(self, df: pd.DataFrame) -> Dict[str, str]:
        """验证文件列并返回列名映射"""
        columns = df.columns.str.lower().str.strip()

        # 查找年龄列
        age_column = None
        for col in ['age', '年龄', 'Age', 'AGE']:
            if col.lower() in columns.values:
                # 找到匹配的原始列名
                matching_indices = columns == col.lower()
                age_column = df.columns[matching_indices].tolist()[0]
                break

        if age_column is None:
            raise ValueError("未找到年龄列，请确保文件包含 'age' 或 '年龄' 列")

        # 查找性别列
        gender_column = None
        for col in ['gender', 'sex', '性别', 'Gender', 'Sex', 'GENDER', 'SEX']:
            if col.lower() in columns.values:
                # 找到匹配的原始列名
                matching_indices = columns == col.lower()
                gender_column = df.columns[matching_indices].tolist()[0]
                break

        if gender_column is None:
            raise ValueError("未找到性别列，请确保文件包含 'gender'、'sex' 或 '性别' 列")

        # 查找人数列（可选）
        count_column = None
        for col in ['number', 'count', 'num', '人数', '数量', 'Number', 'Count', 'NUM', 'COUNT']:
            if col.lower() in columns.values:
                # 找到匹配的原始列名
                matching_indices = columns == col.lower()
                count_column = df.columns[matching_indices].tolist()[0]
                break

        result = {
            'age': age_column,
            'gender': gender_column
        }

        if count_column is not None:
            result['count'] = count_column

        return result

    def _process_population_data(
        self,
        df: pd.DataFrame,
        column_mapping: Dict[str, str]
    ) -> tuple[Dict[int, int], Dict[str, int]]:
        """处理人群数据"""

        # 确定是否有人数列
        has_count_column = 'count' in column_mapping

        if has_count_column:
            # 处理聚合数据格式（年龄、性别、人数）
            return self._process_aggregated_data(df, column_mapping)
        else:
            # 处理个体数据格式（每行一个人）
            return self._process_individual_data(df, column_mapping)

    def _process_individual_data(
        self,
        df: pd.DataFrame,
        column_mapping: Dict[str, str]
    ) -> tuple[Dict[int, int], Dict[str, int]]:
        """处理个体数据（每行代表一个人）"""

        # 清理数据
        df_clean = df[[column_mapping['age'], column_mapping['gender']]].copy()
        df_clean = df_clean.dropna()

        # 处理年龄数据
        age_col = df_clean[column_mapping['age']]
        try:
            ages = pd.to_numeric(age_col, errors='coerce').dropna().astype(int)
        except:
            raise ValueError("年龄列包含无效数据，请确保年龄为数字")

        # 验证年龄范围
        if ages.min() < 0 or ages.max() > 120:
            raise ValueError(f"年龄数据超出合理范围 (0-120)，实际范围: {ages.min()}-{ages.max()}")

        # 处理性别数据
        gender_col = df_clean[column_mapping['gender']].str.strip().str.upper()

        # 标准化性别值
        genders = self._standardize_gender_values(gender_col)

        # 生成分布统计
        age_distribution = ages.value_counts().to_dict()
        gender_distribution = genders.value_counts().to_dict()

        return age_distribution, gender_distribution

    def _process_aggregated_data(
        self,
        df: pd.DataFrame,
        column_mapping: Dict[str, str]
    ) -> tuple[Dict[int, int], Dict[str, int]]:
        """处理聚合数据（年龄、性别、人数）"""

        # 清理数据
        required_cols = [column_mapping['age'], column_mapping['gender'], column_mapping['count']]
        df_clean = df[required_cols].copy()
        df_clean = df_clean.dropna()

        # 处理年龄数据
        try:
            df_clean['age_clean'] = pd.to_numeric(df_clean[column_mapping['age']], errors='coerce').astype(int)
        except:
            raise ValueError("年龄列包含无效数据，请确保年龄为数字")

        # 验证年龄范围
        age_min, age_max = df_clean['age_clean'].min(), df_clean['age_clean'].max()
        if age_min < 0 or age_max > 120:
            raise ValueError(f"年龄数据超出合理范围 (0-120)，实际范围: {age_min}-{age_max}")

        # 处理人数数据
        try:
            df_clean['count_clean'] = pd.to_numeric(df_clean[column_mapping['count']], errors='coerce').astype(int)
        except:
            raise ValueError("人数列包含无效数据，请确保人数为正整数")

        # 验证人数为正数
        if (df_clean['count_clean'] <= 0).any():
            raise ValueError("人数必须为正整数")

        # 处理性别数据
        gender_col = df_clean[column_mapping['gender']].str.strip().str.upper()
        df_clean['gender_clean'] = self._standardize_gender_values(gender_col)

        # 生成年龄分布（按年龄汇总人数）
        age_distribution = df_clean.groupby('age_clean')['count_clean'].sum().to_dict()

        # 生成性别分布（按性别汇总人数）
        gender_distribution = df_clean.groupby('gender_clean')['count_clean'].sum().to_dict()

        return age_distribution, gender_distribution

    def _standardize_gender_values(self, gender_series: pd.Series) -> pd.Series:
        """标准化性别值"""
        gender_mapping = {
            'M': 'Male', 'MALE': 'Male', '男': 'Male', '男性': 'Male',
            'F': 'Female', 'FEMALE': 'Female', '女': 'Female', '女性': 'Female'
        }

        genders = gender_series.map(gender_mapping)
        invalid_genders = genders.isna().sum()
        if invalid_genders > 0:
            raise ValueError(f"发现 {invalid_genders} 个无效的性别值，请使用 M/F、男/女 或 Male/Female")

        return genders

    def _update_import_preview(
        self,
        df: pd.DataFrame,
        age_dist: Dict[int, int],
        gender_dist: Dict[str, int]
    ) -> None:
        """更新导入数据预览"""

        # 检查是否为聚合数据格式
        columns = df.columns.str.lower().str.strip()
        has_count_column = any(col in columns.values for col in ['number', 'count', 'num', '人数', '数量'])

        total_population = sum(age_dist.values())
        total_records = len(df)
        age_range = f"{min(age_dist.keys())}-{max(age_dist.keys())}"
        male_count = gender_dist.get('Male', 0)
        female_count = gender_dist.get('Female', 0)
        male_ratio = male_count / (male_count + female_count) * 100 if (male_count + female_count) > 0 else 0

        # 计算加权平均年龄
        weighted_age_sum = sum(age * count for age, count in age_dist.items())
        avg_age = weighted_age_sum / total_population if total_population > 0 else 0

        if has_count_column:
            # 聚合数据格式
            preview_text = f"""
数据概览 (聚合格式):
• 数据记录数: {total_records:,} 行
• 总人群规模: {total_population:,} 人
• 年龄范围: {age_range}岁
• 性别分布: 男性 {male_count:,} ({male_ratio:.1f}%), 女性 {female_count:,} ({100-male_ratio:.1f}%)
• 加权平均年龄: {avg_age:.1f}岁
• 年龄组数: {len(age_dist)}个

年龄分布 (前10个年龄组):
{self._format_age_distribution_preview(age_dist)}
            """.strip()
        else:
            # 个体数据格式
            preview_text = f"""
数据概览 (个体格式):
• 个体记录数: {total_records:,} 人
• 年龄范围: {age_range}岁
• 性别分布: 男性 {male_count:,} ({male_ratio:.1f}%), 女性 {female_count:,} ({100-male_ratio:.1f}%)
• 平均年龄: {avg_age:.1f}岁
• 独特年龄数: {len(age_dist)}个

年龄分布 (前10个):
{self._format_age_distribution_preview(age_dist)}
            """.strip()

        self.import_preview_text.setPlainText(preview_text)

    def _format_age_distribution_preview(self, age_dist: Dict[int, int]) -> str:
        """格式化年龄分布预览"""
        sorted_ages = sorted(age_dist.items(), key=lambda x: x[1], reverse=True)[:10]
        lines = []
        for age, count in sorted_ages:
            lines.append(f"  {age}岁: {count:,}人")
        return "\n".join(lines)

    def _on_import_mode_changed(self) -> None:
        """导入模式变化处理"""
        use_imported = self.use_imported_checkbox.currentText() == "使用导入文件数据"
        self.config.use_imported_data = use_imported

        # 启用/禁用手动配置控件
        manual_controls = [
            self.size_spinbox, self.age_min_spinbox, self.age_max_spinbox,
            self.age_mean_spinbox, self.age_std_spinbox, self.gender_slider,
            self.distribution_combo
        ]

        for control in manual_controls:
            control.setEnabled(not use_imported)

        # 如果使用导入数据，从导入数据计算配置
        if use_imported and self.config.imported_age_distribution:
            self._update_config_from_imported_data()

        self._generate_preview()

    def _update_config_from_imported_data(self) -> None:
        """从导入数据更新配置"""
        if not self.config.imported_age_distribution or not self.config.imported_gender_distribution:
            return

        age_dist = self.config.imported_age_distribution
        gender_dist = self.config.imported_gender_distribution

        # 计算人群规模
        total_size = sum(age_dist.values())
        self.config.size = total_size

        # 计算年龄统计
        ages = []
        for age, count in age_dist.items():
            ages.extend([age] * count)

        self.config.age_mean = float(np.mean(ages))
        self.config.age_std = float(np.std(ages))
        self.config.age_min = min(age_dist.keys())
        self.config.age_max = max(age_dist.keys())

        # 计算性别比例
        male_count = gender_dist.get('Male', 0)
        total_gender = sum(gender_dist.values())
        self.config.male_ratio = male_count / total_gender if total_gender > 0 else 0.5

        # 设置为自定义分布
        self.config.distribution_type = "imported"

        # 更新UI显示（但不启用控件）
        self.size_spinbox.setValue(self.config.size)
        self.age_mean_spinbox.setValue(self.config.age_mean)
        self.age_std_spinbox.setValue(self.config.age_std)
        self.age_min_spinbox.setValue(self.config.age_min)
        self.age_max_spinbox.setValue(self.config.age_max)
        self.gender_slider.setValue(int(self.config.male_ratio * 100))

        # 更新性别比例标签
        self._on_gender_ratio_changed(int(self.config.male_ratio * 100))

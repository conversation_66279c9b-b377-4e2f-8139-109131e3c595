"""
用户界面模块

包含桌面应用程序的用户界面组件：
- PyQt6主窗口和对话框
- 数据可视化界面
- 参数配置界面
- 结果展示界面
"""

from .desktop.main import Application, MainWindow
from .desktop.windows.config_wizard import PopulationConfigWidget
from .desktop.widgets.simulation_control import SimulationControlWidget
from .desktop.windows.results_viewer import ResultsWindow

__all__ = [
    "Application",
    "MainWindow",
    "PopulationConfigWidget",
    "SimulationControlWidget",
    "ResultsWindow",
]

"""
模拟控制组件

提供模拟运行的控制界面，包括：
- 开始/暂停/停止控制
- 模拟进度显示
- 参数设置界面
- 日志输出显示
"""

from typing import Optional, Dict, Any
from enum import Enum
from dataclasses import dataclass

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QGroupBox, QLabel, QPushButton, QProgressBar,
    QTextEdit, QSpinBox, QDoubleSpinBox, QComboBox,
    QFormLayout, QFrame, QScrollArea, QSplitter
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QFont, QTextCursor


class SimulationStatus(Enum):
    """模拟状态枚举"""
    NOT_STARTED = "未开始"
    RUNNING = "运行中"
    PAUSED = "已暂停"
    COMPLETED = "已完成"
    ERROR = "错误"


@dataclass
class SimulationParameters:
    """模拟参数数据类"""
    start_year: int = 2025
    duration_years: int = 50
    time_step: float = 1.0
    random_seed: Optional[int] = None
    parallel_enabled: bool = True
    max_workers: int = 4


class SimulationControlWidget(QWidget):
    """
    模拟控制面板组件
    
    提供完整的模拟控制界面，包括：
    - 模拟状态管理和控制
    - 进度跟踪和显示
    - 参数配置界面
    - 实时日志输出
    """
    
    # 信号定义
    simulation_start_requested = pyqtSignal(SimulationParameters)
    simulation_pause_requested = pyqtSignal()
    simulation_stop_requested = pyqtSignal()
    simulation_reset_requested = pyqtSignal()
    parameters_changed = pyqtSignal(SimulationParameters)
    
    def __init__(self, parent: Optional[QWidget] = None):
        super().__init__(parent)
        
        # 模拟状态和参数
        self.status = SimulationStatus.NOT_STARTED
        self.parameters = SimulationParameters()
        self.current_progress = 0
        self.current_year = 2025
        self.estimated_time_remaining = 0
        
        # 创建界面
        self._create_ui()
        self._connect_signals()
        
        # 初始化界面状态
        self._update_ui_state()
        
        # 创建定时器用于更新进度
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self._update_progress_display)
    
    def _create_ui(self) -> None:
        """创建用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Vertical)
        
        # 上半部分：控制和参数
        top_widget = QWidget()
        top_layout = QVBoxLayout(top_widget)
        
        # 模拟控制组
        self._create_control_group(top_layout)
        
        # 模拟参数组
        self._create_parameters_group(top_layout)
        
        # 进度显示组
        self._create_progress_group(top_layout)
        
        splitter.addWidget(top_widget)
        
        # 下半部分：日志输出
        self._create_log_group(splitter)
        
        # 设置分割器比例
        splitter.setSizes([400, 200])
        
        layout.addWidget(splitter)
    
    def _create_control_group(self, parent_layout: QVBoxLayout) -> None:
        """创建模拟控制组"""
        group = QGroupBox("模拟控制")
        layout = QHBoxLayout(group)
        
        # 状态显示
        self.status_label = QLabel("状态: 未开始")
        self.status_label.setStyleSheet("font-weight: bold; color: #333333;")
        layout.addWidget(self.status_label)
        
        layout.addStretch()
        
        # 控制按钮
        self.start_button = QPushButton("开始")
        self.start_button.setMinimumWidth(80)
        self.start_button.clicked.connect(self._start_simulation)
        layout.addWidget(self.start_button)
        
        self.pause_button = QPushButton("暂停")
        self.pause_button.setMinimumWidth(80)
        self.pause_button.clicked.connect(self._pause_simulation)
        self.pause_button.setEnabled(False)
        layout.addWidget(self.pause_button)
        
        self.stop_button = QPushButton("停止")
        self.stop_button.setMinimumWidth(80)
        self.stop_button.clicked.connect(self._stop_simulation)
        self.stop_button.setEnabled(False)
        layout.addWidget(self.stop_button)
        
        self.reset_button = QPushButton("重置")
        self.reset_button.setMinimumWidth(80)
        self.reset_button.clicked.connect(self._reset_simulation)
        layout.addWidget(self.reset_button)
        
        parent_layout.addWidget(group)
    
    def _create_parameters_group(self, parent_layout: QVBoxLayout) -> None:
        """创建模拟参数组"""
        group = QGroupBox("模拟参数")
        layout = QFormLayout(group)
        
        # 开始年份
        self.start_year_spinbox = QSpinBox()
        self.start_year_spinbox.setRange(2000, 2100)
        self.start_year_spinbox.setValue(2025)
        layout.addRow("开始年份:", self.start_year_spinbox)
        
        # 模拟时长
        self.duration_spinbox = QSpinBox()
        self.duration_spinbox.setRange(1, 100)
        self.duration_spinbox.setValue(50)
        self.duration_spinbox.setSuffix(" 年")
        layout.addRow("模拟时长:", self.duration_spinbox)
        
        # 时间步长
        self.time_step_spinbox = QDoubleSpinBox()
        self.time_step_spinbox.setRange(0.1, 5.0)
        self.time_step_spinbox.setValue(1.0)
        self.time_step_spinbox.setSingleStep(0.1)
        self.time_step_spinbox.setSuffix(" 年")
        layout.addRow("时间步长:", self.time_step_spinbox)
        
        # 随机种子
        self.random_seed_spinbox = QSpinBox()
        self.random_seed_spinbox.setRange(0, 999999)
        self.random_seed_spinbox.setValue(42)
        self.random_seed_spinbox.setSpecialValueText("随机")
        layout.addRow("随机种子:", self.random_seed_spinbox)
        
        # 并行计算设置
        parallel_layout = QHBoxLayout()
        
        self.parallel_combo = QComboBox()
        self.parallel_combo.addItems(["启用", "禁用"])
        parallel_layout.addWidget(self.parallel_combo)
        
        self.workers_spinbox = QSpinBox()
        self.workers_spinbox.setRange(1, 16)
        self.workers_spinbox.setValue(4)
        self.workers_spinbox.setSuffix(" 线程")
        parallel_layout.addWidget(self.workers_spinbox)
        
        layout.addRow("并行计算:", parallel_layout)
        
        parent_layout.addWidget(group)
    
    def _create_progress_group(self, parent_layout: QVBoxLayout) -> None:
        """创建进度显示组"""
        group = QGroupBox("模拟进度")
        layout = QVBoxLayout(group)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        layout.addWidget(self.progress_bar)
        
        # 进度信息
        info_layout = QGridLayout()
        
        info_layout.addWidget(QLabel("当前年份:"), 0, 0)
        self.current_year_label = QLabel("2025")
        info_layout.addWidget(self.current_year_label, 0, 1)
        
        info_layout.addWidget(QLabel("完成百分比:"), 0, 2)
        self.percentage_label = QLabel("0%")
        info_layout.addWidget(self.percentage_label, 0, 3)
        
        info_layout.addWidget(QLabel("剩余时间:"), 1, 0)
        self.time_remaining_label = QLabel("--:--:--")
        info_layout.addWidget(self.time_remaining_label, 1, 1)
        
        info_layout.addWidget(QLabel("已处理事件:"), 1, 2)
        self.events_processed_label = QLabel("0")
        info_layout.addWidget(self.events_processed_label, 1, 3)
        
        layout.addLayout(info_layout)
        
        parent_layout.addWidget(group)
    
    def _create_log_group(self, parent_widget: QSplitter) -> None:
        """创建日志输出组"""
        group = QGroupBox("模拟日志")
        layout = QVBoxLayout(group)
        
        # 日志文本区域
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMaximumBlockCount(1000)  # 限制最大行数
        
        # 设置等宽字体
        font = QFont("Consolas", 9)
        font.setStyleHint(QFont.StyleHint.Monospace)
        self.log_text.setFont(font)
        
        layout.addWidget(self.log_text)
        
        # 日志控制按钮
        log_buttons_layout = QHBoxLayout()
        
        self.clear_log_button = QPushButton("清空日志")
        self.clear_log_button.clicked.connect(self._clear_log)
        log_buttons_layout.addWidget(self.clear_log_button)
        
        log_buttons_layout.addStretch()
        
        self.save_log_button = QPushButton("保存日志")
        self.save_log_button.clicked.connect(self._save_log)
        log_buttons_layout.addWidget(self.save_log_button)
        
        layout.addLayout(log_buttons_layout)
        
        parent_widget.addWidget(group)
    
    def _connect_signals(self) -> None:
        """连接信号和槽"""
        # 参数变化信号
        self.start_year_spinbox.valueChanged.connect(self._on_parameters_changed)
        self.duration_spinbox.valueChanged.connect(self._on_parameters_changed)
        self.time_step_spinbox.valueChanged.connect(self._on_parameters_changed)
        self.random_seed_spinbox.valueChanged.connect(self._on_parameters_changed)
        self.parallel_combo.currentTextChanged.connect(self._on_parameters_changed)
        self.workers_spinbox.valueChanged.connect(self._on_parameters_changed)
    
    def _update_ui_state(self) -> None:
        """更新界面状态"""
        # 更新状态标签
        self.status_label.setText(f"状态: {self.status.value}")
        
        # 更新按钮状态
        if self.status == SimulationStatus.NOT_STARTED:
            self.start_button.setEnabled(True)
            self.start_button.setText("开始")
            self.pause_button.setEnabled(False)
            self.stop_button.setEnabled(False)
            self.reset_button.setEnabled(True)
            
        elif self.status == SimulationStatus.RUNNING:
            self.start_button.setEnabled(False)
            self.pause_button.setEnabled(True)
            self.stop_button.setEnabled(True)
            self.reset_button.setEnabled(False)
            
        elif self.status == SimulationStatus.PAUSED:
            self.start_button.setEnabled(True)
            self.start_button.setText("继续")
            self.pause_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.reset_button.setEnabled(True)
            
        elif self.status in [SimulationStatus.COMPLETED, SimulationStatus.ERROR]:
            self.start_button.setEnabled(True)
            self.start_button.setText("重新开始")
            self.pause_button.setEnabled(False)
            self.stop_button.setEnabled(False)
            self.reset_button.setEnabled(True)
        
        # 更新参数输入框状态
        params_enabled = self.status in [SimulationStatus.NOT_STARTED, SimulationStatus.COMPLETED, SimulationStatus.ERROR]
        self.start_year_spinbox.setEnabled(params_enabled)
        self.duration_spinbox.setEnabled(params_enabled)
        self.time_step_spinbox.setEnabled(params_enabled)
        self.random_seed_spinbox.setEnabled(params_enabled)
        self.parallel_combo.setEnabled(params_enabled)
        self.workers_spinbox.setEnabled(params_enabled)
    
    def _update_progress_display(self) -> None:
        """更新进度显示"""
        self.progress_bar.setValue(self.current_progress)
        self.percentage_label.setText(f"{self.current_progress}%")
        self.current_year_label.setText(str(self.current_year))
        
        # 格式化剩余时间
        if self.estimated_time_remaining > 0:
            hours = self.estimated_time_remaining // 3600
            minutes = (self.estimated_time_remaining % 3600) // 60
            seconds = self.estimated_time_remaining % 60
            self.time_remaining_label.setText(f"{hours:02d}:{minutes:02d}:{seconds:02d}")
        else:
            self.time_remaining_label.setText("--:--:--")
    
    def _on_parameters_changed(self) -> None:
        """参数变化处理"""
        self._update_parameters_from_ui()
        self.parameters_changed.emit(self.parameters)
    
    def _update_parameters_from_ui(self) -> None:
        """从界面更新参数"""
        self.parameters.start_year = self.start_year_spinbox.value()
        self.parameters.duration_years = self.duration_spinbox.value()
        self.parameters.time_step = self.time_step_spinbox.value()
        
        # 随机种子处理
        seed_value = self.random_seed_spinbox.value()
        self.parameters.random_seed = seed_value if seed_value > 0 else None
        
        # 并行计算设置
        self.parameters.parallel_enabled = self.parallel_combo.currentText() == "启用"
        self.parameters.max_workers = self.workers_spinbox.value()
    
    def _start_simulation(self) -> None:
        """开始模拟"""
        self._update_parameters_from_ui()
        
        if self.status == SimulationStatus.NOT_STARTED:
            self.status = SimulationStatus.RUNNING
            self._add_log("开始模拟...")
            self.simulation_start_requested.emit(self.parameters)
        elif self.status == SimulationStatus.PAUSED:
            self.status = SimulationStatus.RUNNING
            self._add_log("继续模拟...")
        
        self._update_ui_state()
        self.update_timer.start(1000)  # 每秒更新一次
    
    def _pause_simulation(self) -> None:
        """暂停模拟"""
        self.status = SimulationStatus.PAUSED
        self._add_log("模拟已暂停")
        self.simulation_pause_requested.emit()
        self._update_ui_state()
        self.update_timer.stop()
    
    def _stop_simulation(self) -> None:
        """停止模拟"""
        self.status = SimulationStatus.NOT_STARTED
        self._add_log("模拟已停止")
        self.simulation_stop_requested.emit()
        self._reset_progress()
        self._update_ui_state()
        self.update_timer.stop()
    
    def _reset_simulation(self) -> None:
        """重置模拟"""
        self.status = SimulationStatus.NOT_STARTED
        self._add_log("模拟已重置")
        self.simulation_reset_requested.emit()
        self._reset_progress()
        self._update_ui_state()
        self.update_timer.stop()
    
    def _reset_progress(self) -> None:
        """重置进度"""
        self.current_progress = 0
        self.current_year = self.parameters.start_year
        self.estimated_time_remaining = 0
        self.events_processed_label.setText("0")
        self._update_progress_display()
    
    def _add_log(self, message: str) -> None:
        """添加日志消息"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        
        self.log_text.append(formatted_message)
        
        # 自动滚动到底部
        cursor = self.log_text.textCursor()
        cursor.movePosition(QTextCursor.MoveOperation.End)
        self.log_text.setTextCursor(cursor)
    
    def _clear_log(self) -> None:
        """清空日志"""
        self.log_text.clear()
        self._add_log("日志已清空")
    
    def _save_log(self) -> None:
        """保存日志"""
        # TODO: 实现日志保存功能
        self._add_log("日志保存功能待实现")
    
    # 公共接口方法
    def set_status(self, status: SimulationStatus) -> None:
        """设置模拟状态"""
        self.status = status
        self._update_ui_state()
    
    def update_progress(self, progress: int, current_year: int, events_processed: int = 0) -> None:
        """更新进度信息"""
        self.current_progress = progress
        self.current_year = current_year
        self.events_processed_label.setText(str(events_processed))
        self._update_progress_display()
    
    def add_log_message(self, message: str) -> None:
        """添加日志消息（外部调用）"""
        self._add_log(message)
    
    def get_parameters(self) -> SimulationParameters:
        """获取当前参数"""
        self._update_parameters_from_ui()
        return self.parameters

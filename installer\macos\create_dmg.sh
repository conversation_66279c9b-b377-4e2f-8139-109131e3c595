#!/bin/bash
# macOS DMG创建脚本

APP_NAME="结直肠癌筛查模拟器"
APP_VERSION="1.0.0"
DMG_NAME="ColorectalScreeningSimulator-${APP_VERSION}"
SOURCE_DIR="../../dist"
APP_BUNDLE="${APP_NAME}.app"

# 检查应用包是否存在
if [ ! -d "${SOURCE_DIR}/${APP_BUNDLE}" ]; then
    echo "错误: 找不到应用包 ${SOURCE_DIR}/${APP_BUNDLE}"
    exit 1
fi

# 创建临时目录
TEMP_DIR=$(mktemp -d)
echo "创建临时目录: ${TEMP_DIR}"

# 复制应用包到临时目录
cp -R "${SOURCE_DIR}/${APP_BUNDLE}" "${TEMP_DIR}/"

# 创建应用程序文件夹的符号链接
ln -s /Applications "${TEMP_DIR}/Applications"

# 复制背景图片（如果存在）
if [ -f "dmg_background.png" ]; then
    mkdir -p "${TEMP_DIR}/.background"
    cp "dmg_background.png" "${TEMP_DIR}/.background/"
fi

# 创建DMG
echo "创建DMG文件..."
hdiutil create -volname "${APP_NAME}" \
    -srcfolder "${TEMP_DIR}" \
    -ov -format UDZO \
    "${DMG_NAME}.dmg"

# 清理临时文件
rm -rf "${TEMP_DIR}"

echo "DMG创建完成: ${DMG_NAME}.dmg"

# 可选：设置DMG外观
if command -v dmgbuild &> /dev/null; then
    echo "使用dmgbuild优化DMG外观..."
    dmgbuild -s dmg_settings.py "${APP_NAME}" "${DMG_NAME}-styled.dmg"
fi
